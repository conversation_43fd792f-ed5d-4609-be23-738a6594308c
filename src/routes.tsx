import { RouteObject } from 'react-router-dom';
import Layout from './layout';
import ChatPage from './pages/chat';
import Document from './pages/document';
import Home from './pages/home';

const config: RouteObject[] = [
  {
    path: '/document/:id',
    element: <Document />,
  },
  {
    path: '/:agentId',
    element: <Layout />,
    children: [
      {
        path: 'home',
        element: <Home />,
      },
      {
        path: 'new-chat',
        element: <ChatPage />,
      },
      {
        path: 'chat/:id',
        element: <ChatPage />,
      },
    ],
  },
];
export default config;
