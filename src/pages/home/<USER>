.stylesDefault() {
  border-radius: 50%;
  width: 100%;
  height: 100%;
  position: absolute;
}

.treeRoot {
  width: 94px;
  height: 94px;
  border-radius: 50%;
  position: absolute;
  box-shadow: 0 7.52px 18.8px 0 rgb(0 0 0 / 7%);
  z-index: 2;

  &:hover {
    cursor: pointer;
  }

  .insideContent {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    .stylesDefault();

    img {
      transition: transform 0.3s linear, scale 0.3s linear;
      transform: scale(1.03); // 某些webkit内核浏览器会出现模糊问题，缩放后可解决
      scale: 1.03;

      &:hover {
        transform: scale(1.06);
        scale: 1.06;
      }
    }
  }

  .insideBgLayer {
    animation: background-change 4s linear infinite;
    background: conic-gradient(
        from 180deg at 50% 50%,
        rgb(8 148 255 / 80%) 0deg,
        rgb(147 203 255 / 80%) 43deg,
        rgb(240 235 160 / 80%) 108deg,
        rgb(255 144 4 / 80%) 179deg,
        rgb(255 46 84 / 80%) 230deg,
        rgb(201 89 221 / 80%) 283deg,
        rgb(93 104 255 / 80%) 334deg,
        rgb(8 148 255 / 80%) 360deg,
        rgb(147 203 255 / 80%) 403deg
      ),
      #d8d8d8;
    .stylesDefault();
  }
}

@keyframes background-change {
  0% {
    transform: scale(1) rotate(0deg);
    filter: blur(8px);
  }

  50% {
    transform: scale(1.2) rotate(180deg);
    filter: blur(15px);
  }

  100% {
    transform: scale(1) rotate(360deg);
    filter: blur(8px);
  }
}
