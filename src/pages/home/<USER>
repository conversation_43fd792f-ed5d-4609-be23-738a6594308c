/* eslint-disable react/require-default-props */
import { useAgent } from '@/store';
import TreeRoot from './tree-root';
import Questions from '../chat/components/questions';
import styles from './index.module.less';

function Home() {
  const { name } = useAgent();

  const moveCss = {
    position: 'absolute',
    bottom: '0',
    // width: '80%',
    marginBottom: '20px',
    maxWidth: '970px',
  };

  return (
    <div className={styles.container}>
      <div className={styles.containerInner}>
        <TreeRoot
          id="root"
          onClick={() => {}}
          style={{
            width: '83px',
            height: '83px',
            position: 'relative',
            marginTop: '185.8px',
            marginBottom: '28px',
            zIndex: 0,
          }}
        />
        <div className={styles.text}>{name}</div>
        <Questions moveCss={moveCss} />
      </div>
    </div>
  );
}

export default Home;
