import React, { CSSProperties } from 'react';
import { Avatar } from '@echronos/antd';
import { useAgent } from '@/store';
import styles from './tree-root.module.less';

function TreeRoot({
  id,
  style,
  onClick,
}: {
  id: string;
  style: CSSProperties;
  // eslint-disable-next-line no-unused-vars
  onClick: (e: React.MouseEvent) => void;
}) {
  const { avatar } = useAgent();

  return (
    // eslint-disable-next-line jsx-a11y/control-has-associated-label
    <div
      className={styles.treeRoot}
      id={id}
      style={style}
      onClick={onClick}
      role="button"
      tabIndex={-1}
    >
      <div className={styles.insideBgLayer} />
      <div className={styles.insideContent}>
        <Avatar
          style={{
            width: '83px',
            height: '83px',
          }}
          src={avatar}
        />
      </div>
    </div>
  );
}

export default TreeRoot;
