import dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';
import { useRequest } from 'ahooks';
import { useEffect, useRef, useState, UIEventHandler } from 'react';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import { message } from '@echronos/antd';
import useAiSearchStore, { useGeneratingState } from '@/store/useAiSearchStore';
import { useAgent } from '@/store';
import { WldtreeHistoryData } from '@/apis/ech-ai-py/get-wldtree-history-list';
import { getChatHistoryList, getAgentPrologue } from '@/apis';
import AgentAutoMessage from './components/agent-auto-message';
import AskModal from './components/ask-modal';
import ChatItem from './components/chat-item';
import styles from './index.module.less';

/** 搜索结果 */
function SearchResult() {
  const location = useLocation();
  const { id: SID } = useParams();
  const [searchParams] = useSearchParams();
  const { avatar, name, typeId, typeCode, setAgent, prologue, prologueQuestions } = useAgent();

  // 如果手动触发了滚动条, 则停止自动滚动到底部的逻辑, 除非手动置为底部
  const [isAutoScroll, setIsAutoScroll] = useState(true);

  // 任务生成状态
  const generatingState = useGeneratingState();

  // 滚动高度 ref
  const scrollTop = useRef(0);

  // 渲染内容 ref
  const contentRef = useRef<HTMLDivElement>(null);

  /** 页面类型: new-新建搜索, history-历史搜索 */
  const pageType = location.pathname.includes('new-chat') ? 'new' : 'history';

  const { ask, resetResult, updateCurrentAskInfo, resultData, updateResultData, resetController } =
    useAiSearchStore();

  /** 获取会话历史记录 */
  const { run: runGetWldtreeHistoryList } = useRequest(getChatHistoryList, {
    manual: true,
    onSuccess: ({ items }: WldtreeHistoryData) => {
      if (items.length) {
        const { scene_id, last_record_id, collapsed } = items[0];
        try {
          // 储存当前会话相关 id
          updateCurrentAskInfo({
            id: scene_id,
            sceneId: scene_id,
            parentId: last_record_id,
            conversationId: collapsed[0].conversation_id,
          });

          // 笔记、写作、邮件、头脑风暴、文档、自定义智能体
          const chatList = items[0].collapsed.map((item: any) => ({
            tasks: [],
            source: [],
            taskStatus: 'finished',
            id: uuid(),
            title: item?.prompt,
            content: item?.response,
            time: dayjs(item?.submit_at).add(8, 'h').format('YYYY-MM-DD HH:mm:ss'),
          }));

          // @ts-expect-error todo
          updateResultData(chatList.reverse());
        } catch (error) {
          console.warn('[log handle data error]: ', error);
        }
      }

      // 容器滚动到顶部
      setTimeout(() => {
        if (contentRef.current) {
          contentRef.current.scrollTo({ top: 0 });
        }
      });
    },
  });

  /**
   * 提问
   * @param val 关键词
   */
  const handleFetchRequest = (val: string) => {
    ask(val);
  };

  /** 监听元素滚动 */
  const handleScroll: UIEventHandler<HTMLDivElement> = () => {
    if (contentRef.current) {
      // 当前滚动高度小于上次的滚动高度, 认为是手动滚动
      if (contentRef.current.scrollTop < scrollTop.current) {
        // 用户手动滚动：停止自动滚动
        setIsAutoScroll(false);
        scrollTop.current = contentRef.current.scrollTop;
      } else {
        // 用户重新滚动到底部时，恢复自动滚动
        setIsAutoScroll(true);
      }
    }
  };

  useEffect(() => {
    // 自定义智能体需要开场白数据
    if (typeId && typeCode === 'customAgent') {
      getAgentPrologue({ id: typeId })
        .then((res) => {
          // 从响应中提取数据并设置默认值
          const {
            prologue = '',
            prologueQuestions = [],
            description = '默认描述文本'
          } = res || {};

          // 优化判断逻辑：当prologue和prologueQuestions同时为空时使用description
          const shouldUseDescription = (!prologue.trim() && !prologueQuestions.length);

          // 根据条件决定最终的prologue值
          const finalPrologue = shouldUseDescription ? description : prologue;

          setAgent({
            sessionId: res?.sessionId,
            ragflowId: res?.id,
            prologue: finalPrologue,
            prologueQuestions
          });
        })
        .catch(() => {
          message.warning('工作流配置异常, 请检查后再试');
        });
    }

    // 查看详情
    if (pageType === 'history') {
      runGetWldtreeHistoryList({ scene_id: SID });
    }

    // 如果是从 home 跳转进来创建新会话
    if (pageType === 'new' && searchParams.get('keyword')) {
      handleFetchRequest(searchParams.get('keyword') as string);
    }

    return () => {
      // 重置控制器
      resetController();
      // 重置数据
      resetResult();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location]);

  // 监听元素内容变化, 自动滚动到底部
  useEffect(() => {
    const observer = new MutationObserver(() => {
      if (generatingState && isAutoScroll) {
        // 容器滚动到底部
        if (contentRef.current) {
          contentRef.current.scrollTo({
            top: contentRef.current.scrollHeight,
            behavior: 'smooth', // 添加平滑滚动效果
          });
          scrollTop.current = contentRef.current.scrollTop;
        }
      }
    });

    const targetNode = contentRef.current;

    if (targetNode) {
      observer.observe(targetNode, {
        childList: true,
        subtree: true,
        characterData: true,
      });
    }

    return () => {
      observer.disconnect();
    };
  }, [generatingState, isAutoScroll]);

  return (
    <div className={styles['chat-container']}>
      <div ref={contentRef} onScroll={handleScroll} className={styles['content-wrap']}>
        {/* 欢迎语 */}
        {pageType === 'new' && typeCode === 'customAgent' && (
          <AgentAutoMessage
            agentAvatar={avatar}
            agentName={name}
            prologue={prologue}
            tipList={
              prologueQuestions?.map((item) => ({
                id: uuid(),
                title: item,
              })) || []
            }
            onCommand={(val) => {
              handleFetchRequest(val.title);
            }}
          />
        )}
        {/* 结果 */}
        {resultData.map((item, index) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <ChatItem key={`${item.id}-${index}`} index={index} result={item} />
          );
        })}
      </div>

      {/* 提问 */}
      <AskModal />
    </div>
  );
}

export default SearchResult;
