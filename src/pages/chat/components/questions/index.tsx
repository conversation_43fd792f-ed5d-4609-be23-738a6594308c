/* eslint-disable */
import { useState, PropsWithChildren } from 'react';
import { useNavigate, useLocation, matchPath } from 'react-router-dom';
import Icon from '@echronos/echos-icon';
import { useGeneratingState } from '@/store/useAiSearchStore';
import { useAgent, useAiSearchStore } from '@/store';
import { MentionsInput, Mention } from 'react-mentions';
import classNames from 'classnames';
import styles from './index.module.less';

interface Iquestion {
  // 样式
  moveCss?: object;
  // 回车函数
  onEnterAi?: (keyWord: any) => boolean;
  // 点击函数
  onClickAi?: (keyWord: any) => boolean;
}

function Questions({
  moveCss,
  children,
  onEnterAi = () => false,
  onClickAi = () => false,
}: PropsWithChildren<Iquestion>) {
  const { typeId } = useAgent();
  const navigate = useNavigate();
  const location = useLocation();
  const [value, setValue] = useState<string>(''); // 输入框内容

  const { resetCurrentAskInfo } = useAiSearchStore();
  /** 任务请求状态 */
  const generatingState = useGeneratingState();

  // 匹配会话页面(deepseek 或者普通提问)
  const matchChatPage = matchPath('/:agentId/chat/:id', location.pathname);

  // 跳转路由--方法
  const onHandleClick = () => {
    if (value === '') return;
    resetCurrentAskInfo();
    navigate(`/${typeId}/new-chat?keyword=${value}`);
  };

  // ai 点击事件
  const onClickPass = () => {
    if (onClickAi(value)) {
      setValue('');
      return;
    }

    if (value === '') {
      return;
    }

    onHandleClick();
    setValue('');
  };

  // 确保键盘输入
  const handleKeyDown = (event: any) => {
    const ignoredKeys = ['Process', 'Alt', 'Control', 'Shift', 'Meta', 'Escape'];
    if (ignoredKeys.includes(event.key)) {
      return;
    }

    // 监听回车跳转
    if (event.key === 'Enter') {
      event.preventDefault();

      if (onEnterAi(value)) {
        setValue('');
        return;
      }

      if (value === '') {
        return;
      }

      onHandleClick();
      setValue('');
      return;
    }
  };

  const handleChange = (event: any) => {
    setValue(event.target.value);
  };

  return (
    <div className={styles.container} style={{ ...moveCss }}>
      <div
        style={{
          width: '100%',
          background: 'hsla(0, 0%, 100%, 0.5)',
          backdropFilter: 'blur(0)',
          borderRadius: '11px',
          padding: '12px',
          opacity: '1',
          position: 'relative',
        }}
      >
        {children || (
          <MentionsInput
            // 这个id有点特殊==>>dom上这个id绑定的是textarea
            id="mentionsTextarea"
            value={value}
            // 文本值发生更改时调用
            onChange={handleChange}
            // 键盘发生更改时调用
            onKeyDown={handleKeyDown}
            placeholder={matchChatPage ? '继续提问' : '发送消息和智能体进行交流'}
            a11ySuggestionsListLabel="Suggested mentions"
            // 控制窗口显示在光标的上方
            forceSuggestionsAboveCursor
            className={classNames(styles.mentions_input)}
          >
            <Mention
              trigger="@"
              data={[]}
              appendSpaceOnAdd={true}
              className={styles.mentionsMention}
            />
          </MentionsInput>
        )}
        <div className={`${styles.side} ${styles.columnBox}`}>
          <div></div>
          <div className={styles.Centered}>
            {generatingState ? (
              <Icon
                className={styles['btn-stop']}
                name="stop_it"
                size={24}
                onClick={onClickPass}
                title="停止"
              />
            ) : (
              <img
                src="https://img.huahuabiz.com/user_files/20241212/1733967803137608.png"
                style={{ width: '24px', minWidth: '24px', cursor: 'pointer' }}
                onClick={onClickPass}
                alt=""
                title="发送"
              />
            )}
          </div>
        </div>

        <div className={styles.bg} />
      </div>
    </div>
  );
}
export default Questions;
