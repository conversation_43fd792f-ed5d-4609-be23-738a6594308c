// 初始化
.Centered() {
  display: flex;
  justify-content: center;
  align-items: center;
}
.dispersed() {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.side() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.Centered {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dispersed {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.side {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.deekseekBtn {
  cursor: pointer;
  display: flex;
  width: 91px;
  height: 28px;
  justify-content: center;
  border: 1px solid #c6ccd8;
  box-sizing: border-box;
  border-radius: 6px;
  align-items: center;

  &.active {
    border: 1px solid #4f6bfe;
  }

  img {
    width: 71px;
    height: 16px;
  }
}
// css

.container {
  .Centered();

  width: calc(100%);
  border-radius: 11px;
  box-shadow: 0 2px 6px 0 rgb(21 72 191 / 10%), inset 0 0 3px 0 #fff;
}

.bg {
  width: 100%;
  height: 100%;
  padding: 1px;
  position: absolute;
  inset: 0;
  pointer-events: none;
  opacity: 1;
  border-radius: 10px;
  background: conic-gradient(
    from 188deg at 50% 50%,
    #90f -52deg,
    #ffa100 9deg,
    #8000ff 72deg,
    #008cff 81deg,
    #008cff 101deg,
    #60f 125deg,
    #ffa100 183deg,
    #60f 250deg,
    #008cff 262deg,
    #008cff 282deg,
    #90f 308deg,
    #ffa100 369deg
  );
  mask: linear-gradient(#fff 0 100%) content-box, linear-gradient(#fff 0 100%);
  mask-composite: xor;
  mask-composite: subtract;
}

.boxImgChoice {
  width: 28px;
  min-width: 28px;
  height: 28px;
  min-height: 28px;
  margin-right: 12px;
  border-radius: 50%;
  .Centered();
}

.boxImgChoice:hover {
  background-color: #f3f3f3;
}

.imgChoice {
  width: 17px;
  min-width: 17px;
}

.fontSizeS {
  font-family: 'PingFang SC';
  font-size: 16px;
  font-variation-settings: 'opsz' auto;
  background: linear-gradient(90deg, #a555ff 26%, #e947ae 50%, #f96f8c 100%);
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
  margin-left: 8px;
}

.fontSizeP {
  color: #717480;
  font-size: 16px;
  font-weight: 600;
  margin-left: 8px;
  font-family: 'PingFang SC';
}

.columnBox {
  padding-top: 12px;
}

.input {
  font-size: 14px;
  height: 26px;
  max-height: 260px;
  line-height: 26px;
  margin-right: 8px;
  padding: 0;
  padding: 0 12px;
  flex: 1;
  resize: none;
  border: none;
  border-radius: 8px;
}

.minIntelligenceImg {
  width: 20px;
  min-width: 20px;
  height: 20px;
  margin: 0 3px;
}

.minFontSize {
  font-size: 14px;
}

.btn-stop {
  cursor: pointer;
  line-height: 1;
}

// @文字样式, 控制文字样式以及，@的偏移量
.mentionsMention {
  color: #008cff !important;
  position: relative;
  right: 1px;
  bottom: 1px;
  z-index: 1;
  cursor: not-allowed;
  background-color: transparent;
  pointer-events: none;
}

// ===========================>
// 特殊处理
// -----样式1
.mentions_input textarea {
  max-height: 142px;
  padding: 0;
  overflow-y: auto !important;
  border: none;
}

.mentions_input div:nth-child(1) {
  max-height: 140px;
  padding: 0;
  overflow-y: auto;
  border-radius: 7px;
}

.mentions_input > div:nth-child(2) {
  // 弹出框样式
  padding: 8px;
  border-radius: 7px;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}
// -----样式2
.mentions textarea {
  border: none;
}

.mentions div:nth-child(1) {
  max-height: 140px;
  padding: 0;
  overflow-y: auto;
  border-radius: 7px;
}

.mentions > div:nth-child(2) {
  padding: 0;
  border-radius: 7px;
  box-shadow: none;
}
// ==========================>
