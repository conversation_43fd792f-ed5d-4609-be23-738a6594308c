import useUserInfoStore from '@/store/user-info';
import styles from './index.module.less';

interface SelfSideProps {
  title: string;
}

/** 本人 */
function SelfSide({ title }: SelfSideProps) {
  const { user } = useUserInfoStore();

  return (
    <div className={styles['self-side-container']}>
      <div className={styles['ask-content']}>{title}</div>
      <div className={styles.avatar}>
        <img src={user?.user?.avatar} alt="" />
      </div>
    </div>
  );
}

export default SelfSide;
