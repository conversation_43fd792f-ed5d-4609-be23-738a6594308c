import classNames from 'classnames';
import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import copy from 'copy-to-clipboard';
import Icon from '@echronos/echos-icon';
import { message } from '@echronos/antd';
import { MouseEvent, useContext } from 'react';
import NiceModal, { useModal } from '@ebay/nice-modal-react';
import { NiceModalPopover } from '@echronos/editor/dist/core';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { nightOwl } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { ResultItem, useAgent } from '@/store';
import { useGeneratingState } from '@/store/useAiSearchStore';
import SplitterContext from '@/utils/SplitterContext';
import EditPopContent from '../edit-pop-content';
import GeneratingTips from './components/generating-tips';
import SummarizingTips from './components/summarizing-tips';
import styles from './index.module.less';

const markdownComponents = {
  a(props: any) {
    return (
      <a href={props.href} target="_blank" rel="noreferrer">
        {props.href}
      </a>
    );
  },
  code(props: any) {
    const { children, className, ...rest } = props;
    const match = /language-(\w+)/.exec(className || '');
    return match ? (
      <SyntaxHighlighter
        {...rest}
        PreTag="div"
        // eslint-disable-next-line react/no-children-prop
        children={String(children).replace(/\n$/, '')}
        language={match[1]}
        style={nightOwl}
      />
    ) : (
      <code {...rest} className={className}>
        {children}
      </code>
    );
  },
};

interface ResultProps {
  /** 下标 */
  index: number;
  /** 当前内容 */
  result: ResultItem;
}

function OtherSide({ index, result }: ResultProps) {
  const { avatar, name } = useAgent();
  const splitterContext = useContext(SplitterContext);

  /** 编辑弹窗 */
  const editPop = useModal(NiceModal.create(NiceModalPopover));

  /** 任务请求状态 */
  const generatingState = useGeneratingState();

  /** 在文档编辑 */
  const handleEdit2Doc = (e: MouseEvent) => {
    e.stopPropagation();
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    editPop.show({
      rect,
      placement: 'top',
      content: (
        <SplitterContext.Provider value={splitterContext}>
          <EditPopContent result={result} onClose={() => editPop.remove()} />
        </SplitterContext.Provider>
      ),
      popupContainer: `#result-${result.id}-${index}`,
    });
  };

  /** 复制为文档 */
  const handleCopy = (e: MouseEvent) => {
    e.stopPropagation();

    copy(result.content);
    message.success('已复制');
  };

  return (
    <div id={`result-${result.id}-${index}`} className={styles.result}>
      {/* 智能体信息 */}
      <div className={styles.agents}>
        <div className={styles.info}>
          <div className={styles.logo}>
            <img src={avatar} alt="" />
          </div>
          <div className={styles.agentLabel}>{name}</div>
        </div>
      </div>

      {/* 生成任务中提示(@智能体) */}
      {result.taskStatus === 'generating' && <GeneratingTips agent={result.agent} />}

      {/* 生成总结/结果中(@智能体) */}
      {(result.taskStatus === 'executing' || result.taskStatus === 'summarizing') && (
        <SummarizingTips />
      )}

      {/* 结果内容 */}
      <div className={styles.response}>
        <div className={styles['r-content']}>
          <Markdown
            components={markdownComponents}
            className={classNames(styles.markdown, `markdown-${result.id}`)}
            remarkPlugins={[remarkGfm]}
          >
            {result.content}
          </Markdown>
        </div>
      </div>

      {/* 操作项 */}
      {(result.content || !!result.source?.length) && !generatingState && (
        <div className={styles.operates}>
          <div className={styles.btn} onClick={handleEdit2Doc}>
            <img
              alt=""
              width={20}
              height={20}
              style={{ marginRight: '4px' }}
              src="https://img.huahuabiz.com/user_files/20241212/1733969519513217.png"
            />
            在轻墨文档中编辑
          </div>

          <div className={styles.btn} onClick={handleCopy}>
            <Icon name="copy_line" color="#888B98" size={16} style={{ marginRight: '4px' }} />
            复制
          </div>
        </div>
      )}
    </div>
  );
}

export default OtherSide;
