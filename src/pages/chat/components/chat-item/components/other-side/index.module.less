.result {
  width: 100%;
  max-width: var(--con-width);
  padding: 0 16px;
  padding-bottom: 32px;
  position: relative;

  &:last-child {
    padding-bottom: 0;
  }

  .generating {
    @keyframes loading {
      0% {
        content: '.';
      }

      33% {
        content: '..';
      }

      66% {
        content: '...';
      }

      100% {
        content: '.';
      }
    }

    @keyframes gradient-move {
      0% {
        background-position: 100% 0;
      }

      100% {
        background-position: -100% 0;
      }
    }

    color: transparent;
    font-size: 14px;
    font-weight: normal;
    display: inline-block;
    width: fit-content;
    margin: 0 40px;
    animation: gradient-move 4s linear infinite;
    background: linear-gradient(
      270deg,
      #faa24c 0%,
      #ff5b8a 21%,
      #403af4 50%,
      #ff5b8a 79%,
      #faa24c 100%
    );
    background-size: 200% 100%;
    background-position: 100% 0;
    background-clip: text;
    user-select: none;

    &::after {
      content: '•';
      animation: loading 1.5s infinite;
    }
  }

  .thingking {
    margin: 25px 40px 40px;
    padding: 0 12px;
    overflow: hidden;
    position: relative;
    transition: all 0.4s ease;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    pre,
    li {
      color: #888b98;
    }

    ul,
    ol {
      margin-top: 8px;
      padding: 0 0 0 1em;
    }

    li::marker {
      color: #888b98;
    }

    &.height_0 {
      height: 0 !important;
      margin: 0;
      opacity: 0;
    }

    &::before {
      content: ' ';
      display: block;
      width: 1px;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: #c6ccd8;
    }
  }

  .thinkinged {
    color: transparent;
    font-size: 14px;
    font-weight: normal;
    display: inline-block;
    width: fit-content;
    margin: 0 40px;
    background: linear-gradient(
      270deg,
      #faa24c 0%,
      #ff5b8a 21%,
      #403af4 50%,
      #ff5b8a 79%,
      #faa24c 100%
    );
    background-size: 200% 100%;
    background-position: 100% 0;
    background-clip: text;
    user-select: none;
  }

  .agents {
    display: flex;
    margin-top: 12px;
    justify-content: space-between;
    align-items: center;

    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .logo {
        display: flex;
        width: 32px;
        height: 32px;
        margin-right: 8px;
        overflow: hidden;
        justify-content: center;
        box-sizing: border-box;
        border-radius: 16px;
        border: 1px solid #d9d9d9;
        background-color: #fff;
        align-items: center;

        img {
          width: 32px;
          height: 32px;
        }
      }

      .btn-collapse {
        cursor: pointer;
        margin-left: 6px;
      }

      .agentLabel {
        color: #888b98;
        font-size: 14px;
        font-weight: normal;
        line-height: normal;
        font-family: 'PingFang SC';
        letter-spacing: 0;
        font-variation-settings: 'opsz' auto;
      }
    }
  }

  .source {
    display: flex;
    width: 90%;
    margin: 0 -16px 0 40px;
  }

  .response {
    margin: 0 40px 20px;

    .r-text {
      color: #040919;
      font-size: 22px;
      font-weight: 500;
      line-height: 36px;
      margin-bottom: 8px;
    }

    .r-content {
      color: #040919;
      font-size: 15px;
      font-weight: normal;
      line-height: 22.19px;
    }

    .markdown {
      h1 {
        font-size: 30px;
        line-height: 45px;
        margin-top: 34px;

        &:first-child {
          margin-top: 0;
        }
      }

      h2 {
        font-size: 24px;
        line-height: 36px;
        margin-top: 30px;

        &:first-child {
          margin-top: 0;
        }
      }

      h3 {
        font-size: 20px;
        line-height: 30px;
        margin-top: 26px;

        &:first-child {
          margin-top: 0;
        }
      }

      h4 {
        font-size: 16px;
        line-height: 32px;
        margin-top: 22px;

        &:first-child {
          margin-top: 0;
        }
      }

      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        color: #000;
        font-weight: bolder;
      }
    }

    .markdown ul,
    ol {
      margin-top: 8px;
      padding: 0 0 0 2em;
    }

    .markdown {
      ul li,
      ol li {
        line-height: 24px;
        margin-top: 12px;
        margin-bottom: 4px;
        list-style-type: auto;
        list-style-position: outside;
      }
    }

    .markdown {
      ol li {
        strong {
          color: #000;
          font-size: 16px;
        }
      }

      ol > li::marker {
        color: #000;
        font-size: 16px;
      }
    }
  }

  .operates {
    display: flex;
    margin: 0 40px;
    position: relative;

    .btn {
      color: #040919;
      display: flex;
      height: 34px;
      padding: 6px 8px;
      justify-content: center;
      box-sizing: border-box;
      cursor: pointer;
      align-items: center;
      border-radius: 6px;
      border: 1px solid #fff;
      background: rgb(255 255 255 / 50%);

      & + .btn {
        margin-left: 16px;
      }
    }
  }

  :global {
    .nice-modal-popover {
      .ant-popover-arrow {
        display: none;
      }
    }
  }
}
