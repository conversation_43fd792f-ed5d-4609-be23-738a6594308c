.generating {
  @keyframes loading {
    0% {
      content: '.';
    }

    33% {
      content: '..';
    }

    66% {
      content: '...';
    }

    100% {
      content: '.';
    }
  }

  @keyframes gradient-move {
    0% {
      background-position: 100% 0;
    }

    100% {
      background-position: -100% 0;
    }
  }

  color: transparent;
  font-size: 14px;
  font-weight: normal;
  display: inline-block;
  width: fit-content;
  margin: 0 40px;
  animation: gradient-move 4s linear infinite;
  background: linear-gradient(
    270deg,
    #faa24c 0%,
    #ff5b8a 21%,
    #403af4 50%,
    #ff5b8a 79%,
    #faa24c 100%
  );
  background-size: 200% 100%;
  background-position: 100% 0;
  background-clip: text;
  user-select: none;

  &::after {
    content: '•';
    animation: loading 1.5s infinite;
  }
}
