.edit-pop-content {
  color: #040919;
  width: 210px;
  height: 229px;
  overflow-y: auto;
  padding: 12px 0 12px 0;

  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #d8d8d8;
  }

  .create {
    cursor: pointer;
    padding: 0 13px 12px 16px;
    border-bottom: 1px solid #f3f3f3;
  }

  .space-list {
    .space-item {
      cursor: pointer;
      line-height: 22px;
      margin-top: 12px;
      display: flex;
      align-items: center;
      padding: 0 13px 0 16px;

      .space-name {
        flex: 1;
        margin-left: 6px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
