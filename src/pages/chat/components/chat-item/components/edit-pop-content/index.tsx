import { useRequest } from 'ahooks';
import Icon from '@echronos/echos-icon';
import { message, Spin, Popover } from '@echronos/antd';
import { useEffect, useState, MouseEvent } from 'react';
import { permSpaceListApi, PermSpaceListData } from '@/apis';
import { ResultItem } from '@/store/useAiSearchStore';
import spaceCreate from '@/components/space-create';
import docEmitter from '@/event-bus/doc';
import PageListPop from './components/page-list-pop';
import styles from './index.module.less';
import genDocData from './genDocData';

interface EditPopContentProps {
  /** 当前搜索结果数据 */
  result: ResultItem;
  /** 关闭弹窗 */
  onClose: () => void;
}

/** 编辑弹窗 */
function EditPopContent({ result, onClose }: EditPopContentProps) {
  const [spaceList, setSpaceList] = useState<PermSpaceListData[]>([]);

  const { run: runGetSpaceList, loading } = useRequest(permSpaceListApi, {
    manual: true,
    onSuccess(data) {
      try {
        setSpaceList(data.list);
      } catch (error) {
        message.warning(error);
      }
    },
    onError(error) {
      message.warning(error.message);
    },
  });

  /** 新建空间 */
  const handleAddSpace = (e: MouseEvent) => {
    e.stopPropagation();

    // 新建空间弹窗
    spaceCreate({
      onConfirm: () => {},
      customInitDocData: genDocData({ id: result.id, source: result.source }),
    });

    // 新建回调
    onClose();
  };

  useEffect(() => {
    runGetSpaceList();

    docEmitter.off('doc-loaded');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Spin spinning={loading}>
      <div id="edit-pop-content" className={styles['edit-pop-content']}>
        <div className={styles.create} onClick={handleAddSpace}>
          <Icon name="add_line" size={16} /> 新建空间
        </div>

        <div className={styles['space-list']}>
          {spaceList.map((item) => {
            return (
              <Popover
                placement="rightTop"
                content={
                  item.hasChild ? (
                    <PageListPop
                      type="first"
                      resultId={result.id}
                      spaceId={item.spaceId}
                      source={result.source}
                      tasks={result.tasks}
                      onClose={onClose}
                    />
                  ) : null
                }
                getPopupContainer={() =>
                  document.querySelector('#edit-pop-content') as HTMLDivElement
                }
              >
                <div
                  key={item.id}
                  id={`space-${item.spaceId}`}
                  className={styles['space-item']}
                  // onClick={(e) => handleCreatePage(e, item)}
                >
                  <Icon name="file_line" size={16} />
                  <div className={styles['space-name']}>{item.name}</div>
                  {item.hasChild && <Icon name="right_arrow_line" size={16} color="#999EB2" />}
                </div>
              </Popover>
            );
          })}
        </div>
      </div>
    </Spin>
  );
}

export default EditPopContent;
