.page-list-pop-content {
  color: #040919;
  width: 210px;
  height: 229px;
  padding: 12px 0 12px 0;
  display: flex;
  flex-direction: column;

  &::-webkit-scrollbar {
    width: 3px;
    height: 3px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background-color: #d8d8d8;
  }

  .create {
    cursor: pointer;
    padding: 0 13px 12px 16px;
    border-bottom: 1px solid #f3f3f3;
  }

  .page-list {
    flex: 1;
    overflow-y: auto;

    .page-item {
      cursor: pointer;
      line-height: 22px;
      margin-top: 12px;
      display: flex;
      align-items: center;
      padding: 0 13px 0 16px;

      .page-name {
        flex: 1;
        margin-left: 6px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
