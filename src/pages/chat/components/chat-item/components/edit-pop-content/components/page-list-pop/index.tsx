import { v4 as uuid } from 'uuid';
import { useRequest } from 'ahooks';
import Icon from '@echronos/echos-icon';
import { message, Spin, Popover } from '@echronos/antd';
import { useState, useEffect, MouseEvent, useMemo } from 'react';
import {
  getDocPageListFirst,
  getDocPageListChild,
  createDocPageChild,
  createDocPageFirst,
  CreateDocPageFirstParams,
} from '@/apis';
import { DocPageListFirstData } from '@/apis/doc-page/get-doc-page-list-first';
import useAiSearchStore, { ResultItem } from '@/store/useAiSearchStore';
import docEmitter from '@/event-bus/doc';
import { randomEmojiBgImg } from '@/utils/tools';
import styles from './index.module.less';
import genDocData from '../../genDocData';

interface PageListPopProps {
  /** 当前搜索结果 id */
  resultId: string;
  /** 来源数据 */
  source: ResultItem['source'];
  /** 文本连接 */
  tasks: ResultItem['tasks'];
  /** 页面类型: first-第一级页面, extra-其他级页面 */
  type: 'first' | 'extra';
  /** 空间 id */
  spaceId: string;
  /** 页面 id */
  blockId?: string;
  /** 关闭弹窗 */
  onClose: () => void;
}

/** 页面列表 */
function PageListPop({
  resultId,
  source,
  tasks,
  type,
  spaceId,
  blockId,
  onClose,
}: PageListPopProps) {
  const { resultData, currentAskInfo } = useAiSearchStore();
  const { id: sid } = currentAskInfo;

  const [pageList, setPageList] = useState<DocPageListFirstData[]>([]);

  // 根据场景切换请求接口
  const api = type === 'first' ? getDocPageListFirst : getDocPageListChild;

  // 根据场景切换创建接口
  const request = type === 'first' ? createDocPageFirst : createDocPageChild;

  // @ts-expect-error todo
  const { run: runGetDocPageListFirst, loading } = useRequest(api, {
    manual: true,
    onSuccess(data) {
      try {
        setPageList(data.list);
      } catch (error) {
        message.warning(error);
      }
    },
    onError(error) {
      message.warning(error.message);
    },
  });

  /** 整合来源(商品/外部链接) */
  const _source = useMemo(() => {
    // 整合商品来源和文本链接数据
    const linkList: any = [];

    if (tasks?.length) {
      tasks.forEach((item) => {
        if (item.answerData?.linkObj) {
          if (item.answerData?.linkObj.linkList.length) {
            const list = item.answerData?.linkObj.linkList.map((itep) => {
              return {
                id: uuid(),
                type: 0,
                imgUrl: '',
                title: itep.title,
                logoUrl: '',
                logoName: itep.title,
                url: itep.link,
                content: itep.content,
              };
            });

            // eslint-disable-next-line no-unsafe-optional-chaining
            linkList.push(...list);
          }
        }
      });
    }

    if (source?.length) {
      if (tasks?.length) {
        return [...source, ...linkList];
      }
      return source;
    }

    return linkList;
  }, [source, tasks]);

  /** 将搜索内容追加到文档底部 */
  const handleAdd2Doc = (e: MouseEvent, id: string) => {
    e.stopPropagation();

    if (window.microApp?.getData()) {
      const { docJson } = genDocData({ id: resultId, source: _source });

      // 打开文档
      (window.microApp?.getData() as any)?.openDocInDrawer({
        blockId: id,
        docData: docJson,
      });
    }

    onClose();
  };

  /** 新建页面 */
  const handleCreatePage = (e: MouseEvent) => {
    e.stopPropagation();

    const { emoji, picture } = randomEmojiBgImg();

    // 当前会话数据
    const askItem = resultData.find((item) => item.id === sid);

    // 生成文档数据
    const { block, blockId: _blockId, content } = genDocData({ id: resultId, source: _source });

    // 传参
    let params = {} as CreateDocPageFirstParams;

    // 一级页面
    if (type === 'first') {
      params = {
        block,
        content,
        spaceId,
        blockId: _blockId,
        attrs: { pageName: askItem?.title || '未命名的页面' },
        head: {
          background: {
            transform: 50,
            url: `https://img.huahuabiz.com/web-document-img/${picture}`,
          },
          avatar: {
            type: 'icon',
            url: `https://img.huahuabiz.com/emoji/${emoji}`,
          },
          pageName: askItem?.title || '未命名的页面',
          title: askItem?.title || '未命名的页面',
        },
      };
    } else {
      // 其他级页面
      params = {
        block,
        content,
        blockId: blockId as string,
        spaceId: spaceId as string,
        newBlockId: _blockId,
        attrs: { pageName: askItem?.title || '未命名的页面' },
        head: {
          background: {
            transform: 50,
            url: `https://img.huahuabiz.com/web-document-img/${picture}`,
          },
          avatar: {
            type: 'icon',
            url: `https://img.huahuabiz.com/emoji/${emoji}`,
          },
          pageName: askItem?.title || '未命名的页面',
          title: askItem?.title || '未命名的页面',
        },
      };
    }

    request(params)
      .then(() => {
        setTimeout(() => {
          if (window.microApp?.getData()) {
            // 打开文档
            (window.microApp?.getData() as any)?.openDocInDrawer({
              blockId: _blockId,
            });
          }
        }, 500);

        onClose();
      })
      .catch((err: any) => {
        console.warn('[log handleCreatePage err]: ', err);
        message.warn('创建页面失败, 请稍后再试');
      });
  };

  useEffect(() => {
    // @ts-expect-error todo
    runGetDocPageListFirst({ spaceId, blockId });

    docEmitter.off('doc-loaded');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Spin spinning={loading}>
      <div className={styles['page-list-pop-content']}>
        <div className={styles.create} onClick={(e) => handleCreatePage(e)}>
          <Icon name="add_line" size={16} /> 新建页面
        </div>

        <div className={styles['page-list']}>
          {pageList.map((item) => {
            return (
              <Popover
                placement="rightTop"
                content={
                  item.hasChild ? (
                    <PageListPop
                      type="extra"
                      source={source}
                      tasks={tasks}
                      spaceId={spaceId}
                      onClose={onClose}
                      resultId={resultId}
                      blockId={item.blockId}
                    />
                  ) : null
                }
                getPopupContainer={() =>
                  document.querySelector('#edit-pop-content') as HTMLDivElement
                }
              >
                <div
                  key={item.blockId}
                  className={styles['page-item']}
                  onClick={(e) => handleAdd2Doc(e, item.blockId)}
                >
                  <Icon name="file_line" size={16} />
                  <div className={styles['page-name']}>{item.attrs.pageName}</div>
                  {item.hasChild && <Icon name="right_arrow_line" size={16} color="#999EB2" />}
                </div>
              </Popover>
            );
          })}
        </div>
      </div>
    </Spin>
  );
}

PageListPop.defaultProps = {
  blockId: '',
};

export default PageListPop;
