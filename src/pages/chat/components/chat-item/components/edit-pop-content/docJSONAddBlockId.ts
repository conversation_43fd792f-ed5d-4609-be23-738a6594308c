import { v4 as uuid } from 'uuid';

/**
 * 文档数据添加 blockId
 * @param data 文档数据
 * @param blockId 文档页面 id
 * @return 文档数据
 */
const docJSONAddBlockId = (data: any[], blockId: string): any[] => {
  return data.map((item) => {
    if (item.attrs) {
      const bid = uuid();
      return {
        ...item,
        parentId: blockId,
        attrs: { ...item.attrs, blockId: bid },
        content: item.content ? docJSONAddBlockId(item.content, bid) : [],
      };
    }

    return item;
  });
};

export default docJSONAddBlockId;
