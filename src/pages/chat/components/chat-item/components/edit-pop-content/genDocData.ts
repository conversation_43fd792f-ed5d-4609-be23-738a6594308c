import { v4 as uuid } from 'uuid';
import { cloneDeep } from 'lodash';
import { generateJSON } from '@tiptap/core';
import { ResultItem } from '@/store/useAiSearchStore';
import extensionsConfig from '@/components/document/extensions';
import { getBlocks, getContentIds } from '@echronos/editor/dist/extension-on-content-update-all';
import docJSONAddBlockId from './docJSONAddBlockId';

/** 组成文档数据 */
const genDocData = (result: {
  id: string;
  source: ResultItem['source'];
}): { blockId: string; block: any; content: string[]; docJson: any[] } => {
  // 页面 id
  const blockId = uuid();

  // 获取渲染内容 innerHTML
  const htmlStr = document.querySelector(`.markdown-${result.id}`)?.innerHTML as string;

  // 将 innerHTML 转成 tiptap JSON 获取 所有 blocks
  const docContentJSON1 = generateJSON(htmlStr, extensionsConfig).content;

  // 单独插入商品
  if (result.source?.length) {
    docContentJSON1.unshift({
      type: 'cardBox',
      attrs: {
        class: 'node-selectable',
        fullWidth: 'false',
        blockId: '',
        parentId: '',
        rootBlockId: '',
        spaceId: '',
        cardListData: result.source,
      },
    });
  }

  // 补全字段
  const docContentJSON2 = docJSONAddBlockId(cloneDeep(docContentJSON1), blockId);

  // 生成一维 block
  const block = getBlocks(cloneDeep(docContentJSON2));

  // 生成 block 顺序
  const content = getContentIds(cloneDeep(docContentJSON2));

  return { blockId, block, content, docJson: docContentJSON2 };
};

export default genDocData;
