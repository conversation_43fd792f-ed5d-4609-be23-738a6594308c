import { ResultItem } from '@/store';
import OtherSide from './components/other-side';
import SelfSide from './components/self-side';
import styles from './index.module.less';

interface ResultProps {
  /** 下标 */
  index: number;
  /** 当前内容 */
  result: ResultItem;
}

/** 会话 */
function ChatItem({ index, result }: ResultProps) {
  return (
    <div className={styles['chat-item-container']}>
      {/* 自己 */}
      <SelfSide title={result.title} />

      {/* 对方 */}
      <OtherSide index={index} result={result} />
    </div>
  );
}

export default ChatItem;
