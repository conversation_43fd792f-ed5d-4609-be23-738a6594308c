// import { useNavigate } from 'react-router-dom';
import useAiSearchStore, { useGeneratingState } from '@/store/useAiSearchStore';
import Questions from '../questions';
import styles from './index.module.less';

function AskModal() {
  const generating = useGeneratingState();
  const { ask, resetController } = useAiSearchStore();

  /** 发送/暂停 */
  const handleSendOrPause = (keyword: string) => {
    // 结果生成中, 暂停一切生成
    if (generating) {
      resetController();
      return true;
    }

    if (!keyword) {
      return true;
    }

    ask(keyword);
    return true;
  };

  /** 组件内部回车事件 */
  const handleEnter = (keyword: string) => {
    // 生成中
    if (generating) {
      return true;
    }

    // 没有提问内容
    if (!keyword) {
      return true;
    }

    ask(keyword);
    return true;
  };

  return (
    <div className={styles['ask-modal']}>
      <div className={styles.wrap}>
        <Questions onClickAi={handleSendOrPause} onEnterAi={handleEnter} />
      </div>
    </div>
  );
}

AskModal.defaultProps = {};

export default AskModal;
