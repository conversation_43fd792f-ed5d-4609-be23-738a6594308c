import { useState } from 'react';
import { ArrowDown } from '@nutui/icons-react';
import styles from './index.module.less';
import { Tooltip } from 'antd';

interface AgentAutoMessageProps {
  /** 智能体头像 */
  agentAvatar?: string;
  /** 智能体名称 */
  agentName: string;
  /** 消息 */
  prologue: string;
  /** 列表 */
  tipList: {
    id: string | number;
    title: string;
  }[];
  /** tipListItem事件  */
  // eslint-disable-next-line no-unused-vars
  onCommand?: (tipListItem: { id: string | number; title: string }) => void;
}

function AgentAutoMessage({
  agentAvatar = 'https://img.huahuabiz.com/PC/static/img/default_avatar/24.png',
  agentName = 'Ai智能体',
  prologue = '您好!我是慧心助手，您的虚拟客服专家。有什么可以帮助您的吗?',
  tipList = [],
  onCommand,
}: AgentAutoMessageProps) {
  const [collapse, setCollapse] = useState(false);
  return (
    <div className={styles.chatCard}>
      <div className={styles.chatCardContent}>
        <div className={styles.chatHeader}>
          <div className={styles.logo}>
            <img src={agentAvatar} alt="" />
          </div>
          <div className={styles.agentName}>
            <span>{agentName}</span>
            <ArrowDown
              className={collapse ? styles['btn-up'] : styles['btn-down']}
              width="14px"
              height="14px"
              onClick={() => setCollapse(!collapse)}
            />
          </div>
        </div>
        <div
          className={styles.chatContent}
          style={{ gridTemplateRows: !collapse ? '1fr' : '0fr', opacity: collapse ? 0 : 1 }}
        >
          <div style={{ minHeight: 0 }}>{prologue}</div>
        </div>
        {tipList?.length ? (
          <div className={styles.taskListContainer}>
            <div className={styles.taskListContent}>
              {tipList.map((item) => (
                <div
                  key={item.id}
                  className={styles.taskListItem}
                  onClick={() => {
                    onCommand?.(item);
                  }}
                >
                  <Tooltip placement="bottom" title={item?.title}>
                    <div className={styles.taskItem}>{item?.title}</div>
                  </Tooltip>
                </div>
              ))}
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
}

AgentAutoMessage.defaultProps = {
  agentAvatar: 'https://img.huahuabiz.com/PC/static/img/default_avatar/24.png',
  onCommand() {},
};

export default AgentAutoMessage;
