.chatCard {
  width: 100%;
  max-width: var(--con-width);
  margin-bottom: 12px;
}

.chatHeader {
  color: #888b98;
  font-size: 14px;
  display: flex;
  width: 100%;
  padding: 0 18px;
  box-sizing: border-box;
  align-items: center;

  .logo {
    display: flex;
    width: 32px;
    height: 32px;
    margin-right: 8px;
    overflow: hidden;
    justify-content: center;
    box-sizing: border-box;
    border-radius: 50%;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    align-items: center;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .agentName {
    display: flex;
    align-items: center;

    span {
      margin-right: 8px;
    }

    .btn-up {
      animation: up 0.3s forwards;
    }

    .btn-down {
      animation: down 0.3s forwards;
    }
  }
}

@keyframes up {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(-180deg);
  }
}
@keyframes down {
  0% {
    transform: rotate(-180deg);
  }

  100% {
    transform: rotate(0deg);
  }
}

.chatContent {
  display: grid;
  width: 100%;
  line-height: 1.5;
  padding-right: 68px;
  padding-left: 56px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  grid-template-rows: 0fr;
  opacity: 1;
}

.taskListContainer {
  display: flex;
  width: 100%;
  padding-right: 68px;
  padding-left: 56px;
  justify-content: flex-end;
  box-sizing: border-box;

  .taskListContent {
    width: 100%;
    margin-top: 20px;
    padding: 12px;
    border-radius: 16px;
    box-sizing: border-box;
    background: linear-gradient(180deg, #fff 0%, #f4f4ff 100%);
    border: 1px solid #fff;
    box-shadow: 2px 4px 12px 0 rgb(0 24 182 / 8%);
  }

  .taskListItem {
    cursor: pointer;
    width: 100%;
    margin-bottom: 8px;
    padding: 8px 10px;
    overflow: hidden;
    box-sizing: border-box;
    border-radius: 8px;
    background: #fff;
    white-space: nowrap;
    text-overflow: ellipsis;

    &:last-child {
      margin-bottom: 0;
    }
  }
  .taskItem{
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
