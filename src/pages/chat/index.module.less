.chat-container {
  // 容器宽度
  --con-width: 972px;

  display: flex;
  overflow: hidden;
  position: relative;
  flex: 1;
  background-color: transparent;
  align-items: center;
  flex-direction: column;

  &.moblie {
    // 容器宽度
    --con-width: 340px;
  }

  .content-wrap {
    display: flex;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;
    flex: 1;
    align-items: center;
    flex-direction: column;
  }
}
