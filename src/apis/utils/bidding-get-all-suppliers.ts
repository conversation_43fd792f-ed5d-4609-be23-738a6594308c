import { createHttpRequest } from '@echronos/core';

export interface ParamsType {
  mainSheetId: number;
  keyword?: string;
}

export interface CompanyType {
  companyId: number;
  customerId: number;
  companyName: string;
  isAdmittance: boolean;
}

export interface ShareCompanyType {
  shareCompanyId: number;
  shareCompanyName: string;
  shareCustomerList: CompanyType[];
}

export interface ResultType {
  mainSheetId: number;
  companyList: CompanyType[];
  shareCompanyList: ShareCompanyType[];
}

/**
 * 我的招标--资格审核--可选择供应商列表
 */
function biddingGetAllSuppliers(params: ParamsType): Promise<ResultType> {
  return createHttpRequest('ech-bidding')('/v1/bidding/main/get/qualification/supplier', {
    params,
  });
}

export default biddingGetAllSuppliers;
