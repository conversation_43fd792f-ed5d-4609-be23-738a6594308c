import { createHttpRequest } from '@echronos/core';

/**
 * 获取筛选客户的条件
 */
function getFilterCustomerConditions() {
  return createHttpRequest('ech-crm-new')('/v1/customer/getQueryConditionList').then((res: any) => {
    const tags: Record<string, unknown>[] = res.tags || [];
    return {
      sources: (res.customerSourceVOList || []).map((item: Record<string, unknown>) => ({
        id: item.id as number,
        name: item.customerSource as string,
      })),
      industries: (res.customerIndustryVOList || []).map((item: Record<string, unknown>) => ({
        id: item.id as number,
        name: item.industryName as string,
      })),
      tags: tags
        .filter((tag) => !tag.groupId)
        .map((tag) => ({
          id: tag.id,
          name: tag.name,
          children: tags
            .filter((tmp) => tmp.groupId === tag.id)
            .map((tmp) => ({ id: tmp.id, name: tmp.name })),
        })),
    };
  });
}

export default getFilterCustomerConditions;
