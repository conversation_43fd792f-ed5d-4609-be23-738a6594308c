// 我负责的客户 & 我协作的客户
import { createHttpRequest } from '@echronos/core';

export interface responseListType {
  // 1代表客户，2代表供应商，3代表客户&供应商:
  customerType: number;
  // 公司ID:
  companyId: number;
  // 电话:
  phone: string;
  // 公司名称:
  companyName: string;
  // 客户名称:
  customerName: string;
  // 客户ID:
  id: number;
  // 客户编号:
  customerNo: string;
  // 我的客户类型1 负责 0 协作
  type: number;
}

export interface queryCustomerForMeType {
  responseList: responseListType[];
  concertList: responseListType[];
}

interface paramsType {
  keywords?: string;
}

/**
 * 查询我的客户(负责的客户、协作的客户)
 * @param params
 */
function getQueryCustomerForMe(params?: paramsType): Promise<queryCustomerForMeType> {
  return createHttpRequest('ech-crm-new')('/v1/customer/queryCustomerForMe', { params });
}

export default getQueryCustomerForMe;
