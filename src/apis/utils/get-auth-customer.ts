import { createHttpRequest, Paginate, PaginationResponse } from '@echronos/core';

export interface ParamsType extends Paginate {
  keywords?: string;
  customerType?: 0 | 1; // 0：客户 1：供应商
}

export interface ContactType {
  contactUserId: number;
  contactMemberId: number;
  contactName: string;
  phone: string;
  contactCompanyId: number;
  avatar: string;
}

export interface ResultType {
  id: number;
  customerCompanyId: number;
  customerName: string;
  contactList: ContactType[];
}

// 查询客户列表（过滤非认证公司&无联系人）
function getAuthCustomer(params: ParamsType): PaginationResponse<ResultType> {
  return createHttpRequest('ech-crm-new')('/v1/customer/pageForCustomer', { params });
}

export default getAuthCustomer;
