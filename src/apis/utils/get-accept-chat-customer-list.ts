import { createHttpRequest, PaginationResponse } from '@echronos/core';

export interface GetAcceptChatCustomerListParams {
  // 类型 0 全部客户 1 我的客户 2 下属客户 3 协同客户
  type?: 0 | 1 | 2 | 3;
  tagId?: string;
  // 客户来源  0.转介绍、1.自开拓、2.官网、3.自媒体、4.线下活动、5.协会、6.渠道
  customerSource?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  // 客户行业 1 五金建材,2 美妆 ,3 酒店用品 ,4 汽配,5 快消品,6 食品,7 家居用品,8 数码产品, 9 其他
  industryId?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  // 客户类型：1企业客户，2个人客户
  customerCategory?: 1 | 2;
  companyId: number;
  customerId?: number;
  pageSize?: number;
  pageNo?: number;
}

export interface GetAcceptChatCustomerContact {
  avatar: string;
  companyId: number;
  companyName: string;
  contactCompanyId: number;
  contactMemberId: number;
  contactName: string;
  contactUserId: number;
  createMember: number;
  createMemberName: string;
  createTime: number;
  customerId: number;
  decisionFlag: number;
  hasInvite: number;
  id: number;
  image: string;
  isDeleted: number;
  keyDecisionName: string;
  phone: string;
  remark: string;
  responsibleDepartment: unknown[];
  sex: number;
  sexDescription: string;
  updateMember: number;
  updateMemberName: string;
  updateTime: number;
  userName: string;
}

export interface GetAcceptChatCustomerListResult {
  address: string;
  bankAccount: string;
  city: string;
  contactList: GetAcceptChatCustomerContact[];
  contactNumber: number;
  contactUserNumber: number;
  createTime: number;
  customerCategory: number;
  customerId: number;
  customerName: string;
  customerSource: number;
  customerSourceName: string;
  district: string;
  eventTime: number;
  id: number;
  industryId: number | null;
  industryName: string;
  lastEventTime: number;
  openBank: string;
  phone: string;
  province: string;
  publicTime: number;
  region: string;
  registerAddress: string;
  tags: unknown[];
  taxNum: string;
  teamId: number;
  usePsiFlag: number;
  usePsiFlagName: string;
  userName: string;
  wxAccount: string;
}

/**
 * 获取接受群聊的客户列表
 * @param params
 */
function getAcceptChatCustomerList(
  params: GetAcceptChatCustomerListParams
): PaginationResponse<GetAcceptChatCustomerListResult> {
  return createHttpRequest('ech-crm-new')('/v1/customer/getAcceptChatCustomerList', { params });
}

export default getAcceptChatCustomerList;
