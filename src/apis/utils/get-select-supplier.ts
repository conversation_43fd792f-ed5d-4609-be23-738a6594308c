import { createHttpRequest, Paginate, PaginationResponse } from '@echronos/core';

export interface ParamsType extends Paginate {
  companyId?: number;
  keyword?: string;
  type?: 0 | 1 | 2; // 0我的供应商 1共享供应商 2全部供应商
  customerCategory?: 1 | 2; // 1企业客户，2个人客户
  appointType?: 0 | 1 | 2;
  categoryIds?: number[];
}

export interface ContactItemType {
  avatar: string;
  companyId: number;
  companyName: string;
  contactCompanyId: number;
  contactMemberId: number;
  contactName: string;
  contactUserId: number;
  createMember: number;
  createMemberName: string;
  createTime: number;
  customerId: number;
  decisionFlag: number;
  email: string;
  hasInvite: number;
  id: number;
  image: string;
  isDeleted: number;
  keyDecisionName: string;
  phone: string;
  position: string;
  remark: string;
  responsibleDepartment: any[];
  sex: number;
  sexDescription: string;
  updateMember: number;
  updateMemberName: string;
  updateTime: number;
  userName: string;
  wxNum: string;
}

export interface ResultType {
  categoryList: number[];
  companyId: number;
  contactList: ContactItemType[];
  createTime: number;
  customerCategory: number;
  customerCompanyId: number;
  customerName: string;
  customerNo: string;
  customerUserId: number;
  id: number;
  lastEventTime: number;
  locked: number;
  teamId: number;
  userName: string;
}

// 选择人员--选择供应商
function getSelectSupplier(params: ParamsType): PaginationResponse<ResultType> {
  return createHttpRequest('ech-crm-new')('/v1/supplier/getSupplierList', { params });
}

export default getSelectSupplier;
