import { CancelToken, createHttpRequest } from '@echronos/core';
import { formatSearchResults } from '@/components/select-users/search';

export interface SearchAllRelationUsersProps {
  search: string;
  companyId?: number;
  isFilter: 0 | 1 | null;
}

export type {
  SearchRelationUserResult,
  SearchRelationCustomerResult,
} from '@/components/select-users/search';

/**
 * 搜索所有有关系的用户
 * @param params
 * @param cancelToken
 */
function searchAllRelationUsers(params: SearchAllRelationUsersProps, cancelToken?: CancelToken) {
  const config: Record<string, unknown> = { params, cancelToken };
  if (!config.cancelToken) {
    config.only = true;
  }
  return createHttpRequest('ech-search')('/v1/index/search/groupSearch', config).then(
    formatSearchResults
  );
}

export default searchAllRelationUsers;
