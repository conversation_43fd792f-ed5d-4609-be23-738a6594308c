import { createHttpRequest } from '@echronos/core';

export interface GetLastLevelDepartmentResult {
  ancestors: string;
  belongtoCompanyId: number;
  companyId: number;
  deptLeader: number;
  id: number;
  isOpenIm: boolean | 0 | 1;
  orgCategory: number;
  orgLevel: number;
  orgName: string;
  parentId: number;
  remark: string;
  sort: number;
}

/**
 * 获取公司最末级的部门列表
 * @param companyId
 */
function getLastLevelDepartments(
  companyId: number
): Promise<{ list: GetLastLevelDepartmentResult[] }> {
  return createHttpRequest('ech-user')('/v1/user/organization/queryLastOrg', {
    params: { companyId },
  });
}

export default getLastLevelDepartments;
