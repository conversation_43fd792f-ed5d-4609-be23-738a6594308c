import { CancelToken, createHttpRequest, PaginationResponse } from '@echronos/core';
import type { UserFriendResult } from '../user/get-user-friends';

export interface RecentChatListResult extends UserFriendResult {
  id: number; // 会话id
  sessionType: number; // 会话类型
}

/**
 * 获取最近聊天列表
 * @param cancelToken {CancelToken} 取消请求 token
 * @param autoToast {boolean} 是否只请求一次
 */
function getRecentChatList(
  cancelToken?: CancelToken,
  autoToast?: boolean
): PaginationResponse<RecentChatListResult> {
  return createHttpRequest('ech-imc')('/v2/imc/session/recently/private', {
    cancelToken,
    autoToast,
  });
}

export default getRecentChatList;
