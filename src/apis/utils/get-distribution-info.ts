import { createHttpRequest, PaginationResponse } from '@echronos/core';

/**
 * 获取行业列表
 */

export interface NycbngShareContactsResponsesData {
  id: number;
  orgId: number;
  userId: number;
  memberId: number;
  memberType: number;
  nickname: string;
  avatar: string;
}

export interface DistributorInfoResponsesData {
  companyId: number;
  companyName: string;
  companyLogo: string;
  isAuth: number;
  tags: string[];
  isPersonalCompany: number;
  nycbngShareContactsResponses: NycbngShareContactsResponsesData[];
}

export interface GetDistributionInfoResult {
  id: number;
  orgCategory: number;
  orgName: string;
  orgLogo: string;
  orgType: number;
  distributorInfoResponses: DistributorInfoResponsesData[];
}

function getDistributionInfo(params: {
  companyId: number;
  type?: number;
}): PaginationResponse<GetDistributionInfoResult[]> {
  return createHttpRequest('ech-user')('/v1/nycbng/org/distributor/member/info', { params });
}

export default getDistributionInfo;
