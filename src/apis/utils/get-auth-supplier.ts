import { createHttpRequest, Paginate, PaginationResponse } from '@echronos/core';

export interface ParamsType extends Paginate {
  companyId?: number;
  keyword?: string;
  customerCategory?: 1 | 2; // 1企业客户，2个人客户
  type?: 0 | 1 | 2; // 0 我的供应商 1 共享供应商 2 全部供应商
}

export interface ContactType {
  contactUserId: number;
  contactMemberId: number;
  contactName: string;
  phone: string;
  contactCompanyId: number;
  avatar: string;
}

export interface ResultType {
  id: number;
  customerCompanyId: number;
  customerName: string;
  contactList: ContactType[];
}

// 查询供应商列表（过滤非认证公司&无联系人）
function getAuthSupplier(params: ParamsType): PaginationResponse<ResultType> {
  return createHttpRequest('ech-crm-new')('/v1/supplier/pageForCustomer', { params });
}

export default getAuthSupplier;
