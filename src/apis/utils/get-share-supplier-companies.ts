import { createHttpRequest, Paginate, PaginationResponse } from '@echronos/core';

export interface paramsType extends Paginate {
  keyword?: string;
}

export interface ResultType {
  id: number;
  companyId: number;
  companyName: string;
}

// 获取共享供应商公司
function getShareSupplierCompanies(params: paramsType): PaginationResponse<ResultType> {
  return createHttpRequest('ech-crm-new')('/v1/supplier/bidding/query', { params });
}

export default getShareSupplierCompanies;
