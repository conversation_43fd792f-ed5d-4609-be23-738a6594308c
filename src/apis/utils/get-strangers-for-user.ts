import { CancelToken, createHttpRequest, PaginationResponse } from '@echronos/core';
import { UserFriendResult } from '../user/get-user-friends';

/**
 * 获取用户的陌生人
 * @param cancelToken {CancelToken} 取消请求 token
 * @param autoToast {boolean} 是否只请求一次
 */
function getStrangersForUser(
  cancelToken?: CancelToken,
  autoToast?: boolean
): PaginationResponse<UserFriendResult> {
  return createHttpRequest('ech-imc')('/v2/imc/friend/my/stranger', {
    cancelToken,
    autoToast,
  }).then(({ list, pagination }: Record<string, any>) => ({
    pagination,
    list: list.map((item: { friendId: number }) => ({
      ...item,
      id: item.friendId,
    })),
  }));
}

export default getStrangersForUser;
