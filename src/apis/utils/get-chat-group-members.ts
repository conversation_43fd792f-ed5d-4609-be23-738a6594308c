import { createHttpRequest, isN<PERSON>ber, <PERSON><PERSON>ate, PaginationResponse } from '@echronos/core';

export interface GetChatGroupMembersProps extends Paginate {
  groupId: number;
}

export interface GetChatGroupMemberResult {
  alias: string;
  avatar: string;
  isShop: 0 | 1 | boolean;
  jurisdiction: number;
  nickname: string;
  stageName: string;
  memberName: string;
  relation: number;
  remark: string;
  shopId: number;
  toUserId: number;
  whetherGroupMaster: 0 | 1 | boolean;
}

/**
 * 获取聊天群聊成员列表
 * @param groupId
 */
function getChatGroupMembers(
  groupId: number | GetChatGroupMembersProps
): PaginationResponse<GetChatGroupMemberResult> {
  let data;
  if (isNumber(groupId)) {
    data = { groupId };
  } else {
    data = groupId;
  }
  return createHttpRequest('ech-imc')('/v1/imc/group/member/list', { data, method: 'POST' });
}

export default getChatGroupMembers;
