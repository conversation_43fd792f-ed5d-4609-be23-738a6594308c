import { createHttpRequest } from '@echronos/core';

export interface GetCompanyOrganizationsProps {
  id?: number; // 组织架构id
  companyId?: number; // 公司id
  type?: number; // type类型: 4/CRM用户
  groupId?: number; // 圈聊id
  isInvitation?: boolean; // 是否包含未激活成员
}

export interface CompanyOrganizationMemberResult {
  avatar: string;
  belongtoCompanyId: number;
  companyId: number;
  id: number;
  isDefault: 0 | 1 | boolean;
  isShow: 0 | 1 | boolean;
  lastLogin: 0 | 1 | boolean;
  name: string;
  nickname: string;
  phone: string;
  position: string;
  roleId: string;
  roleName: string;
  stageName: string;
  realName: string;
  status: number;
  userId: number;
  isInvitation: number;
  accountType: number;
}

export interface CompanyOrganizationBreadcrumbResult {
  id: number;
  orgName: string;
}

export interface CompanyOrganizationResult {
  amount: number;
  ancestors: string;
  belongtoCompanyId: number;
  companyId: number;
  id: number;
  isOpenIm: 0 | 1 | boolean;
  isShow: 0 | 1 | boolean;
  members: CompanyOrganizationMemberResult[];
  orgCategory: number;
  orgLevel: number;
  orgName: string;
  parentId: number;
  remark: string;
  sort: number;
  status: number;
  userIds: number[];
}

export interface GetCompanyOrganizationsResult {
  count: number;
  breadcrumbs: CompanyOrganizationBreadcrumbResult[];
  organizations: CompanyOrganizationResult[];
  members: CompanyOrganizationMemberResult[];
}

/**
 * 获取公司部门信息
 * @param data {GetCompanyOrganizationsProps} 请求数据
 * @param cancelToken {CancelToken} 取消请求 token
 * @param autoToast {boolean} 是否只请求一次
 */
function getCompanyOrganizations(
  data: GetCompanyOrganizationsProps,
  cancelToken?: any,
  autoToast?: boolean
): Promise<GetCompanyOrganizationsResult> {
  return createHttpRequest('ech-system')('/v1/system/organization/org/id', {
    data,
    cancelToken,
    autoToast,
    method: 'POST',
  }).then((res: any) => {
    const breadcrumbs = res.crumbsList.sort(
      (item1: { array: number }, item2: { array: number }) => item1.array - item2.array
    );
    return {
      breadcrumbs,
      count: res.count,
      organizations: res.organizationVO,
      members: res.memberVO,
    };
  });
}

export default getCompanyOrganizations;
