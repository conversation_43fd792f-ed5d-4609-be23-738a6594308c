import { createHttpRequest, Paginate } from '@echronos/core';

export interface ParamsType extends Paginate {
  orgId?: number;
  roleName?: string;
}

export interface RoleList {
  id: number;
  roleName: string;
  orgId: number;
  roleType: number;
  sort: number;
  nums: number;
  companyId: number;
  isFlow: number;
}

export interface RoleListResult {
  records: RoleList[];
}

/**
 * 获取角色列表
 */
function roleList(params?: ParamsType): Promise<RoleListResult> {
  return createHttpRequest('ech-system')('/v1/system/role/list', { params });
}

export default roleList;
