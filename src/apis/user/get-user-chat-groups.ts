import { CancelToken, createHttpRequest, PaginationResponse } from '@echronos/core';

// 0 默认查询我加入和创建的全部群聊 1 查询我创建的群聊 2 查询我加入的群聊
export type GetUserJoinChatGroupType = 0 | 1 | 2;

export interface GetUserJoinChatGroupResult {
  avatar: string;
  groupMemberUserIds: number[];
  groupNum: number;
  groupType: number;
  id: number;
  maxMemberNum: number;
  name: string;
  sendTime: number;
  sessionId: number;
}

/**
 * 获取用户群聊列表
 * @param myGroupType
 * @param cancelToken {CancelToken} 取消请求 token
 * @param autoToast {boolean} 是否只请求一次
 */
function getUserChatGroups(
  myGroupType: GetUserJoinChatGroupType,
  cancelToken?: CancelToken,
  autoToast?: boolean
): PaginationResponse<GetUserJoinChatGroupResult> {
  return createHttpRequest('ech-imc')('/v1/imc/group/list', {
    cancelToken,
    autoToast,
    params: { myGroupType },
  });
}

export default getUserChatGroups;
