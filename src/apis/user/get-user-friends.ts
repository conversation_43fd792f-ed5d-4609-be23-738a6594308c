import { CancelToken, createHttpRequest, PaginationResponse } from '@echronos/core';

export interface UserFriendResult {
  userId: number; // 用户id
  nickname: string; // 用户昵称
  avatar: string; // 好友头像
  relation: number; // 用户关系
  companyName: string; // 公司名称
  stageName: string; // 企业昵称（别名）
  remark: string; // 备注
}

/**
 * 获取当前用户所有好友列表
 * @param cancelToken {CancelToken} 取消请求 token
 * @param autoToast {boolean} 是否只请求一次
 */
function getUserFriends(
  cancelToken?: CancelToken,
  autoToast?: boolean
): PaginationResponse<UserFriendResult> {
  return createHttpRequest('ech-imc')('/v2/imc/friend/my/friend', { cancelToken, autoToast }).then(
    ({ list = [], pagination }: Record<string, any>) => ({
      pagination,
      list: list.map((item: { friendId: number }) => ({
        ...item,
        id: item.friendId,
      })),
    })
  );
}

export default getUserFriends;
