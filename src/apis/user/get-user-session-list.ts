import { createHttpRequest } from '@echronos/core';
import { SessionTypes } from '@/utils/session';

/**
 * 回话详情
 */
export interface SessionResult {
  avatar: string;
  companyId: number;
  companyName: string;
  createUser: number;
  groupType: number;
  id: number;
  isDeleted: 0 | 1 | boolean;
  isDisturb: 0 | 1 | boolean;
  isIntoGroup: 0 | 1 | boolean;
  isShield: 0 | 1 | boolean;
  isShop: 0 | 1 | boolean;
  isTop: 0 | 1 | boolean;
  lang: 'cn' | 'en';
  lastMessage: any; // eslint-disable-line
  lastMsgTime: number;
  name: string;
  readNum: number;
  relation: number;
  remark: string;
  selfBusiness: number;
  serviceCompanyId: number;
  sessionType: number;
  shopId: number;
  shopType: number;
  showRead: number;
  typeCode: string;
  typeId: number;
  typeTagUrl: string;
  typeTagValue: number | string;
  updateTime: number;
  userNum: number;
  userId: number;
  at: boolean;
  draft: null | string;
  openType: 0 | 1 | 2 | 3 | 4;
  useStatus: 0 | 1 | boolean;
}

export const formatSession = (item: SessionResult): SessionResult => {
  const session: SessionResult = {
    ...item,
    at: false,
    draft: null,
  };
  if (session.sessionType === SessionTypes.SHOP) {
    session.typeCode = `shop-${session.typeId}`;
  } else if (session.sessionType === SessionTypes.CUSTOMER_SERVICE) {
    session.typeCode = `im-${session.typeCode}`;
  }
  if (!session.avatar) {
    session.avatar = `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/PC/static/img/default_avatar/24.png`;
  }
  return session;
};

function getUserSessionList(all?: true): Promise<SessionResult[]> {
  return createHttpRequest('ech-imc')('/v1/imc/session/list', { method: 'POST' }).then(
    (res: { list: SessionResult[] }) => {
      let list = (res && res.list) || [];
      if (!all) {
        list = list.filter((item) => !item.isDeleted);
      }
      return list.map(formatSession);
    }
  );
}

export default getUserSessionList;
