import { createHttpRequest, PaginationResponse } from '@echronos/core';

export interface GetUserCompanyInfoListCompany {
  companyName: string;
  companyNature: number;
  id: number;
  isDeleted: 0 | 1 | boolean;
  logoUrl: string;
}

export interface GetUserCompanyInfoListOrg {
  amount: number;
  ancestors: string;
  belongtoCompanyId: number;
  companyId: number;
  id: number;
  isOpenIm: 0 | 1 | boolean;
  isShow: 0 | 1 | boolean;
  members: [];
  orgCategory: number;
  orgLevel: number;
  orgName: string;
  parentId: number;
  remark: string;
  sort: number;
  status: number;
  userIds: number[];
}

export interface GetUserCompanyInfoListResult {
  company: GetUserCompanyInfoListCompany;
  orgList: GetUserCompanyInfoListOrg[];
}

/**
 * 获取当前用户加入的所有（已认证）公司及下属部门的信息
 */
function getUserCompanyInfoList(
  params: {
    type?: number;
  },
  autoToast?: boolean
): PaginationResponse<GetUserCompanyInfoListResult> {
  return createHttpRequest('ech-system')('/v1/system/nycbng/company/userId', { params, autoToast });
}

export default getUserCompanyInfoList;
