import http from './http';

interface ParamsType {
  message: string;
}

export interface CreateAgentDraftType {
  avatar: string; // 头像
  title: string; // 智能体名称
  description: string; // 简介A
  roleSet: string; // 角色设定
  prologue: string; // 开场白
  prologueQuestions: string[]; // 开场白问题
}

function getCreateAgentDraft(params: ParamsType): Promise<CreateAgentDraftType> {
  return http('/v1/agent/get/pre/info', { data: params, method: 'POST' });
}

export default getCreateAgentDraft;
