import http from './http';

export interface AgentList {
  id: string; // 主键ID
  userId: string; // 用户ID
  virtualUserId: number; // 虚拟用户ID
  avatar: string; // 头像
  title: string; // 标题
  description: string; // 描述
  updateDate: string; // 更新日期
  updateTime: number; // 更新时间
}

// 我的智能体 列表
function getAgentList(): Promise<AgentList> {
  return http('/v1/agent/list', { method: 'GET' });
}

export default getAgentList;
