import { CreateAgentDraftType } from './get-create-agent-draft';
import http from './http';

interface ParamsType {
  id: number;
}

export interface AgentPrologueType extends CreateAgentDraftType {
  id: string;
  sessionId: string;
}

function getAgentPrologue(params: ParamsType): Promise<AgentPrologueType> {
  return http('/v1/agent/get/prologue', { params, method: 'GET', autoToast: false });
}

export default getAgentPrologue;
