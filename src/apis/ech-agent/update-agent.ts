import http from './http';
import { CreateAgentDraftType } from './get-create-agent-draft';
import { AgentDetailType } from './get-agent-detail';

export interface UpdateAgentParamsType extends CreateAgentDraftType {
  id: string | number;
}

// 更新智能体
function updateAgent(params: UpdateAgentParamsType): Promise<AgentDetailType> {
  return http('/v1/agent/update', { data: params, method: 'PUT' });
}

export default updateAgent;
