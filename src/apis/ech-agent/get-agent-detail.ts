import { CreateAgentDraftType } from './get-create-agent-draft';
import http from './http';

interface ParamsType {
  id: number;
}

export interface AgentDetailType extends CreateAgentDraftType {
  id: string;
  name: string;
  virtualUserId?: number;
}

// 获取智能体详情
function getAgentDetail(params: ParamsType): Promise<AgentDetailType> {
  return http('/v1/agent/get/detail', { params, method: 'GET' });
}

export default getAgentDetail;
