import { createHttpRequest, getToken } from '@echronos/core';

export interface ChatHistoryListItemResType {
  action_type: number;
  conversation_id: string;
  err_msg: string | null;
  model: string;
  prompt: string;
  provider: number;
  response: string;
  scene: string;
  scene_id: string;
  record_id: number;
  status: number;
  submit_at: string;
  full_response: any;
  first_prompt: string;
  agentRequestType: string;
}

export interface ChatHistoryResType {
  total: number;
  items: ChatHistoryListItemResType;
}

interface ChatHistoryParam {
  page_size?: number;
  page_num?: number;
  scene_id?: string;
  virtual_user_id?: string;
  agentRequestType?: string | string[];
}

export interface ChatHistoryData extends ChatHistoryParam {
  total?: number;
  total_pages?: number;
  has_next: boolean;
  items: ChatHistoryListItemResType[];
}

function getChatHistoryList(data?: ChatHistoryParam) {
  return createHttpRequest('ech-ai-py')('/v1/openai/history/collapsed', {
    data,
    method: 'POST',
    // @ts-expect-error todo
    headers: { authorization: getToken() },
  });
}

export default getChatHistoryList;
