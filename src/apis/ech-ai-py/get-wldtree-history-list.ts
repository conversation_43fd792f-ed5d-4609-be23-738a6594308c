import { getToken } from '@echronos/core';
import request from './base';

export interface HistoryListItemResType {
  collapsed: any[];
  action_type: number;
  conversation_id: string;
  err_msg: string | null;
  model: string;
  first_prompt: string;
  provider: number;
  response: string;
  scene: string;
  scene_id: string;
  record_id: number;
  status: number;
  submit_at: string;
  full_response: any;
  last_record_id: number;
}

export interface HistoryResType {
  items: HistoryListItemResType;
}

interface WldtreeHistoryParam {
  page_size?: number;
  page_num?: number;
  scene_id?: string;
}

export interface WldtreeHistoryData extends WldtreeHistoryParam {
  total?: number;
  total_pages?: number;
  has_next: boolean;
  items: HistoryListItemResType[];
}

function getWldtreeHistoryList(data?: WldtreeHistoryParam) {
  return request('/v1/openai/history', {
    data,
    method: 'POST',
    // @ts-expect-error todo
    headers: { authorization: getToken() },
  });
}

export default getWldtreeHistoryList;
