import http from '@/apis/http/sitemanager';

export interface UpdateDocPageMoveParams {
  blockId: string;
  targetParentBlockId: string;
  sourceParentBlockId: string;
  targetParentContent: string[];
  sourceParentType: string;
  targeParentType: string;
}

// 轻墨文档页面管理-移动页面
function updateDocPageMove(data: UpdateDocPageMoveParams) {
  return http('/v1/qm/page/move', {
    method: 'POST',
    data,
  });
}

export default updateDocPageMove;
