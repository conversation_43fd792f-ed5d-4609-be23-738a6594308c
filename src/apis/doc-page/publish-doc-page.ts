import http from '@/apis/http/sitemanager';

export interface PublishDocPageParams {
  /** 页面blockId */
  blockId: string;
  /** 标题 */
  title: string;
  /** 内容 */
  content?: string;
  /** 内容json */
  jsonContent: string;
  /** 封面图 */
  coverImageList?: string[];
  /** 是否智能分发 */
  isIntelligent: number;
  /** 关联的话题id */
  topicIds?: number[];
}

// 轻墨文档页面管理-发布笔记
function publishDocPage(data: PublishDocPageParams) {
  return http('/v1/qm/page/push/note', {
    method: 'POST',
    data,
  });
}

export default publishDocPage;
