import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';
import { PermSpaceListData } from '@/apis';

export interface DocPageListShareParams {
  spaceId: string;
}

// 轻墨文档页面管理-查询当前登录用户有权看到的某个页面下子页面
function getDocPageListShare(): PaginationResponse<PermSpaceListData> {
  return http('/v1/qm/page/get/share/first/page', { method: 'GET' });
}

export default getDocPageListShare;
