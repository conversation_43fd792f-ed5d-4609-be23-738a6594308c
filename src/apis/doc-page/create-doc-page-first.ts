import http from '@/apis/http/sitemanager';

export interface CreateDocPageFirstParams {
  block: any[];
  content: string[];
  spaceId: string;
  blockId: string;
  newBlockId?: string;
  attrs: {
    pageName: string;
  };
  head?: any;
}

// 轻墨文档页面管理-新建一级页面（基于空间）
function createDocPageFirst(data: CreateDocPageFirstParams) {
  return http('/v1/qm/page/add/first/page', {
    method: 'POST',
    data,
  });
}

export default createDocPageFirst;
