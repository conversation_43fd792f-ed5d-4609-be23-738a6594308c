import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';
import { DocPageListFirstData } from './get-doc-page-list-first';

export interface DocPageListChildParams {
  blockId: string; // 块ID(父页面blockId，和父节点ID保持一致即可)
}

// 轻墨文档页面管理-查询当前登录用户有权看到的某个页面下子页面
function getDocPageListChild(
  params: DocPageListChildParams
): PaginationResponse<DocPageListFirstData> {
  return http('/v1/qm/page/get/child/page', {
    method: 'GET',
    params,
  });
}

export default getDocPageListChild;
