import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';

export interface DocPageListFirstParams {
  spaceId: string;
}

export interface DocPageListFirstData {
  blockId: string;
  spaceId: string;
  type: string; // 类型(blockType:1.space-空间 2.block-块 3.page)
  attrs: {
    pageName: string;
    logo: string;
  };
  logo: string;
  name: string;
  key: string;
  hasChild: boolean;
}

// 轻墨文档页面管理-查询当前登录用户某个空间下有权看到的一级页面
function getDocPageListFirst(
  params: DocPageListFirstParams
): PaginationResponse<DocPageListFirstData> {
  return http('/v1/qm/page/get/first/page', {
    method: 'GET',
    params,
  });
}

export default getDocPageListFirst;
