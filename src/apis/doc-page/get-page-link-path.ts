import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';

export interface PageLinkPathParams {
  blockId: string;
}

export interface PageLinkPathData {
  id: string;
  name: string;
  type: string;
  logo: string;
  key: string;
  blockId: string;
  spaceId: string;
}

// 轻墨文档页面管理-查询链路路径
function getPageLinkPath(params: PageLinkPathParams): PaginationResponse<PageLinkPathData> {
  return http('/v1/qm/site/page/link/pathh', { method: 'GET', params });
}

export default getPageLinkPath;
