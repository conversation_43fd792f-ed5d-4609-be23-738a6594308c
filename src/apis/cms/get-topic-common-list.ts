import { PaginationResponse } from '@echronos/core';
import request from '../http/cms';

export interface TopicItem {
  id: number; // ID
  name: string; // 话题名称
  status: number; // 公共池话题启用状态：0-禁用 1-启用
  source: number; // 来源：1用户自建 2官方运营
  assNotesNum: number; // 关联笔记数
  viewNum: number; // 浏览量
  createTime: string; // 创建时间
}

interface GetTopicListParams {
  keyword?: string;
  tenantId?: string; // 租户ID;
  pageNo?: number; // 页码;
  pageSize?: number; // 大小;
}

/**
 * 查询话题列表
 */
function getTopicCommonList(params: GetTopicListParams): PaginationResponse<TopicItem> {
  return request('/v1/note/topic/common/page/list', {
    params,
    method: 'GET',
  });
}

export default getTopicCommonList;
