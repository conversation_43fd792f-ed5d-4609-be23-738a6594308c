import request from '../http/sitemanager';

export interface IBlock {
  blockId: string; // 块ID
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  attrs: Record<string, any>;
  type: string; // 组件类型
  content: string[];
  parentId: string; // 父节点ID
  spaceId: string; // 空间id
  createUser: number; // 创建人
  createTime: string; // 创建时间
  updateUser: number; // 更新人
  updateTime: string; // 更新时间
}

export interface PageBlockDetail extends IBlock {
  block: Record<string, IBlock>;
  tenantId: string;
  tenantLogo: string;
  tenantName: string;
  domain: string;
  blockId: string;
  seoDesc: string; // seo 描述
  seoKeyword: string; // seo 关键字
  browserIcon: string; // 浏览器图标
  browserTitle: string; // 浏览器标题
  siteBrowserIcon: string;
  siteSeoKeyword: string;
  siteBrowserTitle: string;
  siteSeoDesc: string;
  companyId: number;
  defHeaderContainer: any; // 头部容器
  defHeaderContainerBlock: any; // 头部内容
  defFooterContainer: any; // 底部容器
  defFooterContainerBlock: any; // 底部内容
}

export interface GetPageBlocksParams {
  tenantId?: string;
}

/**
 * 查询用户首页
 */
function getHomePage(params?: GetPageBlocksParams): Promise<PageBlockDetail> {
  return request('/v1/site/page/pre/index', {
    params,
    method: 'GET',
    only: true,
  });
}

export default getHomePage;
