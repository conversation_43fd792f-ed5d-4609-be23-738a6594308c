import { PaginationResponse } from '@echronos/core';
import request from '../http/sitemanager';

export interface SitePage {
  blockId: string; // 块ID
  spaceId: string; // 空间id
  type: string; // 类型(blockType:1.space-空间 2.block-块 3.page)
  attrs: Record<string, any>;
  content: string[];
  parentId: string; // 父节点ID
  createUser: number; // 创建人
  createTime: string; // 创建时间
  updateUser: number; // 更新人
  updateTime: string; // 更新时间
  publishStatus: number; // 发布状态
}

export interface SiteSpace {
  spaceId: string; // 空间id
  spaceName: string; // 空间名称
  pageList: SitePage[];
}

interface GetSitePagesParams {
  keyWord?: string;
}

/**
 * 建站首页(空间-查询当前公司建站空间的页面)
 */
function getSitePages(params: GetSitePagesParams): PaginationResponse<SiteSpace> {
  return request('/v1/site/index', {
    params,
    method: 'GET',
  });
}

export default getSitePages;
