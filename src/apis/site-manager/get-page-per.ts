import { DocumentHeadCoverData } from '@echronos/millet-ui';
import request from '../http/sitemanager';

export interface IBlock {
  blockId: string; // 块ID
  attrs: Record<string, any>;
  type: string; // 组件类型
  content: string[];
  head: DocumentHeadCoverData; // 页面编辑头部信息
  parentId: string; // 父节点ID
  spaceId: string; // 空间id
  createUser: number; // 创建人
  createTime: string; // 创建时间
  updateUser: number; // 更新人
  updateTime: string; // 更新时间
  publishStatus?: number; // 发布状态
}

export interface PagePerDetail extends IBlock {
  block: Record<string, IBlock>;
  perCodes: string[];
}

interface getPagePerParams {
  blockId: string;
}

// 轻墨文档页面管理-查询页面权限
function getPagePer(params: getPagePerParams): Promise<PagePerDetail> {
  return request('/v1/qm/page/get/per/code', {
    params,
    method: 'GET',
    autoToast: false,
  });
}

export default getPagePer;
