import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';

export interface PermMemberAddListData {
  id: number;
  name: string;
  logo: string;
  type: number;
  memberId: number;
  memberType: number;
}

export interface PermMemberAddListParams {
  roleId?: string;
  spaceId: string;
}

// 轻墨文档成员管理-添加成员选人组件回显
function permMemberAddListApi(
  params: PermMemberAddListParams
): PaginationResponse<PermMemberAddListData> {
  return http('/v1/qm/member/space/echo', { method: 'GET', params });
}

export default permMemberAddListApi;
