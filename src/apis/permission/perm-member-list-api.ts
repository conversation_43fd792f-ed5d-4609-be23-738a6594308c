import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';

export interface PermMemberListParams {
  roleId: string;
  spaceId: string;
}

export interface PermMemberListData {
  id: number;
  blockId: string;
  spaceId: string;
  mpId: number;
  memberId: number;
  userLogo: string;
  name: string;
  roleName: string;
  memberType: number;
  memberRoleId: number;
  isOwner: boolean;
}

// 轻墨文档成员管理-查询空间成员列表
function permMemberListApi(params: PermMemberListParams): PaginationResponse<PermMemberListData> {
  return http('/v1/qm/member/space/list', { method: 'GET', params });
}

export default permMemberListApi;
