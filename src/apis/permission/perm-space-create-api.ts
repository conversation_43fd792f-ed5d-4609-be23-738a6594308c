import http from '@/apis/http/sitemanager';

export interface PermSpaceCreateParams {
  name: string;
  description: string;
  logo: string;
  blockId?: string;
  spaceId?: string;
  block: any[];
  content: any[];
  head?: any;
}

// 轻墨文档空间管理-创建空间
function permSpaceCreateApi(data: PermSpaceCreateParams) {
  return http('/v1/qm/space/add', { method: 'POST', data });
}

export default permSpaceCreateApi;
