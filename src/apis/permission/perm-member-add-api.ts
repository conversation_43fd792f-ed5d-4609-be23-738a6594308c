import http from '@/apis/http/sitemanager';

interface MemberListParams {
  roleId: number;
  memberType: number;
}

export interface PermMemberAddParams {
  roleId: number;
  spaceId: string;
  addType: number;
  memberList: MemberListParams[];
}

// 轻墨文档成员管理-添加成员
function permMemberAddApi(data: PermMemberAddParams) {
  return http('/v1/qm/member/add', { method: 'POST', data });
}

export default permMemberAddApi;
