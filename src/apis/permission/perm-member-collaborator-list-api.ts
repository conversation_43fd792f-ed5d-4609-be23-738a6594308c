import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';
import { PermMemberAddListData } from './perm-member-add-list-api';

export interface PermMemberCollaboratorListParams {
  blockId: string;
}

// 轻墨文档成员管理-添加成员选人组件回显
function permMemberCollaboratorListApi(
  params: PermMemberCollaboratorListParams
): PaginationResponse<PermMemberAddListData> {
  return http('/v1/qm/member/block/echo', { method: 'GET', params });
}

export default permMemberCollaboratorListApi;
