import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';

export interface PermSpaceApplyListParams {
  spaceId: string;
}

export interface PermSpaceApplyListData {
  id: string;
  spaceId: string;
  applyReason: string;
  status: number;
  memberId: number;
  userLogo: string;
  nickName: string;
  companyName: string;
  createTime: string;
}

// 轻墨文档空间管理-成员申请列表
function permSpaceApplyListApi(
  params: PermSpaceApplyListParams
): PaginationResponse<PermSpaceApplyListData> {
  return http('/v1/qm/space/apply/list', { method: 'GET', params, autoToast: false });
}

export default permSpaceApplyListApi;
