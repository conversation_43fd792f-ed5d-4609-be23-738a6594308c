import http from '@/apis/http/sitemanager';

interface MemberListData {
  memberId: number;
  memberType: number;
  roleId: number;
}

export interface PermMemberCollaboratorParams {
  blockId: string;
  spaceId: string;
  memberList: MemberListData[];
}

// 轻墨文档成员管理-邀请协作者
function permMemberCollaboratorApi(data: PermMemberCollaboratorParams) {
  return http('/v1/qm/member/invite/collaborator', { method: 'POST', data });
}

export default permMemberCollaboratorApi;
