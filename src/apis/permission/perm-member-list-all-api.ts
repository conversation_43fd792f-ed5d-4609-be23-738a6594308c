import http from '@/apis/http/sitemanager';

interface PermMemberListAllParams {
  blockId: string;
}

export interface SpaceMemberData {
  id: number;
  blockId: string;
  spaceId: string;
  mpId: number;
  status: number;
  memberId: number;
  userLogo: string;
  name: string;
  roleId: number;
  roleName: string;
  type: number;
  companyName: string;
  spaceName: string;
  memberType: number;
  memberRoleId: number;
}

interface OwnerData {
  userLogo: string;
  name: string;
  roleId: number;
  roleName: string;
}

export interface PermMemberListAllData {
  collaborators: SpaceMemberData[];
  spaceMembers: SpaceMemberData[];
  spaceEditors: SpaceMemberData[];
  owner: OwnerData;
  totalCount: number;
}

// 轻墨文档成员管理-查询页面成员列表
function permMemberListAllApi(params: PermMemberListAllParams): Promise<PermMemberListAllData> {
  return http('/v1/qm/member/block/list', { method: 'GET', params });
}

export default permMemberListAllApi;
