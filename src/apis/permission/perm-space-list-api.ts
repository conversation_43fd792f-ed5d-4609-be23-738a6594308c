import http from '@/apis/http/sitemanager';
import { PaginationResponse } from '@/apis/utils/utils';

export interface PermSpaceListData {
  id: string;
  spaceId: string;
  name: string;
  logo: string;
  description: string;
  roleIds: string[];
  type: string;
  key: string;
  blockId: string;
  children: PermSpaceListData[];
  spaceName: string;
  spaceLogo: string;
  pageList: PermSpaceListData[];
  content: string[];
  ownerId: number;
  hasChild: boolean;
  parentId: string;
  perCodes: string[];
  isOwner: boolean;
  attrs: {
    pageName: string;
    logo: string;
  };
  /** 空间类型 1成员私有空间  2团队空间  3模板空间 */
  spaceType: number;
}

// 轻墨文档空间管理-查询当前用户有权看到的空间列表
function permSpaceListApi(): PaginationResponse<PermSpaceListData> {
  return http('/v1/qm/space/list', { method: 'GET' });
  // return http('/v1/qm/page/list/space', { method: 'GET' });
}

export default permSpaceListApi;
