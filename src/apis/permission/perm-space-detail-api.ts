import http from '@/apis/http/sitemanager';

interface PermSpaceDetailParams {
  spaceId: string;
}

export interface PermSpaceDetailData {
  spaceId: string;
  name: string;
  logo: string;
  description: string;
  blockId: string;
  isOwner: boolean;
  perCodes: string[];
  ownerId: number;
  key: string;
}

// 轻墨文档空间管理-空间详情
function permSpaceDetailApi(params: PermSpaceDetailParams): Promise<PermSpaceDetailData> {
  return http('/v1/qm/space/detail', { method: 'GET', params, autoToast: false });
}

export default permSpaceDetailApi;
