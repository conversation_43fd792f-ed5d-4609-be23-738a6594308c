import http from './http';

export interface AgentCenterItemType {
  agent_id: string;
  avatar: string;
  company_id: number;
  create_user: string;
  created_at: string;
  description: string;
  greeting_message: string;
  greeting_question: string[];
  id: string;
  member_id: number;
  name: string;
  role: string;
  session_id: string | null;
  status: number;
  system_message: string;
  updated_at: string;
  user_id: number;
  biz_type: string; // 写文章 article 写笔记 note 头脑风暴 head 写邮件 mail
}

export interface GetAgentsListType {
  agent_name?: string; // 搜索关键词
}

function GetAgentsListApiCenter(params: GetAgentsListType) {
  return http('/api/agents/list/center', {
    params,
    method: 'GET',
  });
}

export default GetAgentsListApiCenter;
