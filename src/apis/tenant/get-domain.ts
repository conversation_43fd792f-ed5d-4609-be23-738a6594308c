import request from '../http/tenant';

export interface ResDomain {
  domain: string;
}

export interface GetDomainParams {
  /** 租户ID */
  tenantId?: string;
  /** 来源：0-web域名 1-微信小程序 2-华世界的小程序（开放平台） */
  source: number;
}

/**
 * 查询用户首页
 */
function getDomain(params?: GetDomainParams): Promise<ResDomain> {
  return request('/v1/tenant/get/domain', {
    params,
    method: 'GET',
  });
}

export default getDomain;
