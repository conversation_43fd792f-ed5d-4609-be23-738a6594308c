import { HTMLAttributes, ReactEventHandler } from 'react';
import { useMemoizedFn } from 'ahooks';
import classNames from 'classnames';
import { addWebIframe } from '@echronos/core';
import { getOriginUrl } from '@/utils/tools';
import './index.less';

interface PropsType extends HTMLAttributes<HTMLIFrameElement> {
  name?: string;
  src: string;
}

function Iframe({ name, title, className, onLoad, ...props }: PropsType) {
  const host = getOriginUrl();
  const onLoadIframe: ReactEventHandler<HTMLIFrameElement> = useMemoizedFn((e) => {
    const ele = e.target as HTMLIFrameElement | null;
    if (ele) {
      addWebIframe(ele);
    }
    onLoad?.(e);
  });

  return (
    <iframe
      {...props}
      name={name}
      title={title}
      className={classNames('eos-iframe', className)}
      onLoad={onLoadIframe}
      src={/http|https/.test(props.src) ? props.src : `${host}${props.src}`}
    />
  );
}

Iframe.defaultProps = {
  name: 'SystemSettingIframe',
  // eslint-disable-next-line react/default-props-match-prop-types
  title: 'SystemSettingIframe',
};

export default Iframe;
