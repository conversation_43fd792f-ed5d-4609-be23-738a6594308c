import classNames from 'classnames';
import { mergeAttributes, Node, NodeViewProps } from '@tiptap/core';
import { ReactNodeViewRenderer, NodeViewWrapper } from '@tiptap/react';
import { getDefaultAttrs } from '@echronos/editor/dist/core';
import styles from './style.module.less';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    insertMention: {
      /**
       * 插入提及
       */
      // eslint-disable-next-line no-unused-vars
      insertMention: (props: { [key: string]: any }) => ReturnType;
    };
  }
}

const NodeTextplaceholder = Node.create<any>({
  name: 'textplaceholder',

  group: 'inline',

  inline: true,

  selectable: false,

  atom: true,

  addAttributes() {
    return {
      ...getDefaultAttrs(),
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-id'),
        renderHTML: (attributes) => {
          if (!attributes.id) {
            return {};
          }

          return {
            'data-id': attributes.id,
          };
        },
      },

      textplaceholder: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-textplaceholder'),
        renderHTML: (attributes) => {
          if (!attributes.textplaceholder) {
            return {};
          }

          return {
            'data-textplaceholder': attributes.textplaceholder,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'textplaceholder',
      },
    ];
  },

  renderHTML({ HTMLAttributes }: any) {
    return ['textplaceholder', mergeAttributes(HTMLAttributes)];
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () =>
        this.editor.commands.command(({ tr, state }) => {
          let isMention = false;
          const { selection } = state;
          const { empty, anchor } = selection;

          if (!empty) {
            return false;
          }

          // eslint-disable-next-line consistent-return
          state.doc.nodesBetween(anchor - 1, anchor, (node, pos) => {
            if (node.type.name === this.name) {
              isMention = true;
              tr.insertText('', pos, pos + node.nodeSize);

              return false;
            }
          });

          return isMention;
        }),
    };
  },

  addCommands() {
    return {
      insertMention:
        (props) =>
        ({ commands }) =>
          commands.insertContent(props),
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(({ node }: NodeViewProps) => {
      window.console.log('textplaceholder-node', node);
      const { blockId, textplaceholder } = node.attrs;

      return (
        <NodeViewWrapper
          className={classNames(styles.nodeTextplaceholderWrap, `node-${blockId}`)}
          as="span"
          contenteditable="true"
        >
          <span
            style={{ cursor: 'pointer' }}
            className={classNames(
              styles.nodeTextplaceholderRender,
              this.options.HTMLAttributes.class
            )}
          >
            {textplaceholder}
          </span>
        </NodeViewWrapper>
      );
    });
  },
});

export default NodeTextplaceholder;
