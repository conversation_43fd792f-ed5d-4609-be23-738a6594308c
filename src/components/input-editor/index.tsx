/* eslint-disable react/require-default-props */
// import { useState, useRef, MouseEvent as ReactMouseEvent } from 'react';
import classNames from 'classnames';
import { EditorOptions, Content, EditorEvents } from '@tiptap/core';
import EditorCore from '@echronos/editor/dist/core-v2';
import { editorProviderId, editorProviderParentId } from '@echronos/editor/dist/core';
import extensionsConfig from './extensions';
import './index.less';

interface EditorProps {
  editable: boolean;
  /** 渲染内容 */
  content?: Content;
  /** 编辑器初始化 */
  onCreate?: EditorOptions['onCreate'];
  /** 编辑器更新 */
  onUpdate?: EditorOptions['onUpdate'];
  /** 编辑器更新同步 form onChange */
  onChange?: MultipleParamsFn<[data: any]>;
}

function InputEditor({ editable, content, onCreate, onUpdate, onChange }: EditorProps) {
  // const startYRef = useRef(0); // 坐标起始 Y

  /** 编辑器更新 */
  const handleUpdate = (props: EditorEvents['update']) => {
    onUpdate?.(props);
    onChange?.(props.editor.getJSON());
  };

  /** 编辑器初始化 */
  const handleCreate = (props: EditorEvents['create']) => {
    onCreate?.(props);
  };

  // const handleMouseMove = (e: MouseEvent) => {
  //   if (startYRef.current) {
  //     let animationFrameId = 0;
  //     cancelAnimationFrame(animationFrameId);

  //     animationFrameId = requestAnimationFrame(() => {
  //       const diff = e.clientY - startYRef.current;
  //       setHeight(height + diff);
  //     });
  //   }
  // };

  // const handleMouseUp = () => {
  //   if (startYRef.current) {
  //     document.removeEventListener('mousemove', handleMouseMove);
  //     document.removeEventListener('mouseup', handleMouseUp);
  //   }
  // };

  // const handleMouseDown = (e: ReactMouseEvent) => {
  //   startYRef.current = e.clientY;
  //   document.addEventListener('mousemove', handleMouseMove);
  //   document.addEventListener('mouseup', handleMouseUp);
  // };

  return (
    <div
      className={classNames('wldtree-editor-wrap')}
      id={editorProviderParentId}
      // style={{ height }}
    >
      <EditorCore
        editable={editable}
        content={content}
        extensions={extensionsConfig}
        editorProps={{
          attributes: {
            id: editorProviderId,
          },
        }}
        onCreate={handleCreate}
        onUpdate={handleUpdate}
      />
    </div>
  );
}

export default InputEditor;
