.wldtree-editor-wrap {
  font-size: 14px;
  background-color: #fff;
  min-height: 30px;
  max-height: 69px;
  overflow-y: auto;

  /* 整个滚动条 */
  &::-webkit-scrollbar {
    width: 0; /* 宽度 */
    // background-color: #e7f0f4; /* 背景色 */
  }

  /* 滚动条内的滑块 */
  // &::-webkit-scrollbar-thumb {
  //   background-color: #c6ccd8; /* 滑块颜色 */
  //   border-radius: 2px; /* 圆角 */
  // }

  /* 滚动条轨道 */
  // &::-webkit-scrollbar-track {
  //   background-color: #e3eff4; /* 轨道颜色 */
  //   border-radius: 6px; /* 圆角 */
  // }
}
