import { Plugin<PERSON><PERSON> } from '@tiptap/pm/state';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { selectableNodeCls } from '@echronos/editor/dist/core';
import ParagraphNode from '@echronos/editor/dist/node-paragraph';
import NodeImgLabel from '../node-img-label';
import NodeTextplaceholder from '../node-textplaceholder';

const extendCls = (extensionName: string, isDraggabe = true) =>
  `${isDraggabe ? selectableNodeCls : ''} node-${extensionName}`;

const commonClsConfig = () => ({
  HTMLAttributes: {
    class: selectableNodeCls,
  },
});

const extensionsConfig = [
  StarterKit.configure({
    // @ts-expect-error todo
    history: true,
    paragraph: {
      HTMLAttributes: {
        class: extendCls('paragraph'),
      },
    },
    heading: false,
    horizontalRule: false,
    dropcursor: {
      color: '#4096ff',
      width: 2,
      class: 'block__dropcursor',
    },
    code: {
      HTMLAttributes: {
        class: 'block__inline-code',
      },
    },
    codeBlock: { ...commonClsConfig() },
    bulletList: false,
    orderedList: false,
    listItem: false,
  }),
  ParagraphNode,
  Placeholder.configure({
    placeholder() {
      return '请输入你想要的内容';
    },
    emptyEditorClass: 'block__editor-empty',
    emptyNodeClass: 'block__node-empty',
    includeChildren: true,
  }),
  NodeImgLabel.configure({
    HTMLAttributes: {
      class: 'tiptap-extension-imgLabel',
    },
    suggestion: {
      pluginKey: new PluginKey('imgLabel'),
      items: ({ query }: any) => [query],
    },
  }),
  NodeTextplaceholder.configure({
    HTMLAttributes: {
      class: 'tiptap-extension-textplaceholder',
    },
    suggestion: {
      pluginKey: new PluginKey('textplaceholder'),
      items: ({ query }: any) => [query],
    },
  }),
];

export default extensionsConfig;
