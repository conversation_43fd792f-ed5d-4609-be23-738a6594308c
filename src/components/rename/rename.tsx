import ReactDOM from 'react-dom';
import { PopupMask } from '@echronos/editor/dist/core';
import { Col, Input, Row, message } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import { TextAreaRef } from 'antd/lib/input/TextArea';
import { SelectEmojiPicture } from '@echronos/millet-ui';

interface renamePopParams {
  pos: {
    top: number;
    left: number;
  };
  pageName: string;
  logoUrl: string;
  // eslint-disable-next-line no-unused-vars
  renameChange: (name: string, logo: string) => void;
}

const defaultPagePng = 'https://img.huahuabiz.com/user_files/2024625/1719301693061916.png';

function RenamePopContent({
  pos,
  pageName,
  logoUrl,
  renameChange,
  close,
}: renamePopParams & { close: () => void; logoUrl: string }) {
  const nameIpt = useRef(null as unknown as TextAreaRef);
  const [logo, setLogo] = useState<string>(logoUrl);
  const [hover, setHover] = useState(false);
  const submit = () => {
    // const val = nameIpt.current.input?.value;
    const val = nameIpt.current.resizableTextArea?.textArea.value;

    if (!val) {
      message.error('请输入页面名称');
      return;
    }
    if (val !== pageName || logoUrl !== logo) {
      renameChange(val, logo);
    }
    close();
  };

  useEffect(() => {
    setLogo(logoUrl);
  }, [logoUrl]);

  return (
    <>
      <PopupMask visible onClick={submit} className="pop_mask" />
      <div
        className={classNames('sider-widget__rename show')}
        style={{
          height: '40px',
          left: `${pos.left - 208}px`,
          top: `${pos.top + 36}px`,
          zIndex: '1000',
        }}
      >
        <Row align="middle">
          <Col
            style={{
              height: '40px',
            }}
          >
            <SelectEmojiPicture
              type="emoji"
              onChange={(e) => {
                setLogo(e);
              }}
              onRemove={() => {
                setLogo('');
              }}
            >
              <div className="page-icon">
                <img src={logo || defaultPagePng} alt="" />
              </div>
            </SelectEmojiPicture>
          </Col>
          <Col
            flex={1}
            style={{
              height: '40px',
            }}
          >
            <Input.TextArea
              ref={nameIpt}
              onPressEnter={submit}
              className="rename-input"
              style={{ borderColor: hover ? '#441EFF' : '' }}
              defaultValue={pageName}
              placeholder="请输入页面名称"
              autoSize={{ minRows: 1, maxRows: 6 }}
              onFocus={() => setHover(true)}
              onBlur={() => setHover(false)}
            />
          </Col>
        </Row>
      </div>
    </>
  );
}

function renamePop() {
  const container = document.createElement('div');
  const body = document.getElementsByTagName('body')[0];
  function close() {
    ReactDOM.unmountComponentAtNode(container);
    body.removeChild(container);
  }

  function render(params: renamePopParams) {
    ReactDOM.render(
      <RenamePopContent
        {...params}
        close={() => {
          close();
        }}
      />,
      container
    );
    body.appendChild(container);
  }
  function open(params: renamePopParams) {
    render(params);
  }

  return {
    open,
  };
}

export default renamePop;
