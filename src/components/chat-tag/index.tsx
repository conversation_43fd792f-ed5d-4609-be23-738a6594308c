import { HTMLAttributes, ReactNode, useMemo } from 'react';
import classNames from 'classnames';
import { SessionResult } from '@/apis/user/get-user-session-list';
import { GroupTypes, SessionTypes, UserRelations } from '@/utils/session';
import colleague from '@/assets/chat-tags/colleague.png';
import stranger from '@/assets/chat-tags/stranger.png';
import inner from '@/assets/chat-tags/inner.png';
import full from '@/assets/chat-tags/full.png';
import department from '@/assets/chat-tags/department.png';
import anonymous from '@/assets/chat-tags/anonymous.png';
import customer from '@/assets/chat-tags/customer.png';
import ai from '@/assets/chat-tags/ai.png';
import cooperation from '@/assets/chat-tags/cooperation.png';
import customerService from '@/assets/chat-tags/customer-service.png';
import proprietary from '@/assets/chat-tags/proprietary.png';
import shielding from '@/assets/chat-tags/shielding.png';
import helper from '@/assets/chat-tags/helper.png';
import styles from './index.module.less';

export interface ChatTagProps extends Omit<HTMLAttributes<HTMLSpanElement>, 'children'> {
  userId?: number;
  value: null | SessionResult | { relation: number } | { groupType: number };
}

function ChatTag({ userId, value, className, ...props }: ChatTagProps) {
  const [image, isStranger] = useMemo(() => {
    let img: ReactNode = null;
    let isStrangerTag = false;

    if (value) {
      // 华南城商服仅返回客户标签
      if (
        import.meta.env.BIZ_APP_PLATFORM_NO === '1' &&
        'groupType' in value &&
        value.groupType === GroupTypes.CUSTOMER
      ) {
        img = <img src={customer} alt="客户" />;
      } else if ('groupType' in value && value.groupType === GroupTypes.COOPERATION) {
        img = <img src={cooperation} alt="合作" />;
      } else if (import.meta.env.BIZ_APP_PLATFORM_NO !== '1') {
        if ('sessionType' in value) {
          const { sessionType } = value as SessionResult;
          const currentUserId = userId || (import.meta.env.SSR ? 0 : window.CURRENT_USER_ID || 0);
          if (sessionType === SessionTypes.CUSTOMER_SERVICE && value.createUser === currentUserId) {
            img = <img src={customerService} alt="客服" />;
          } else if (sessionType === SessionTypes.SHOP && value.selfBusiness) {
            img = <img src={proprietary} alt="自营" />;
          } else if (sessionType === SessionTypes.AI_ASSISTANT) {
            img = <img src={ai} alt="智能AI" />;
          } else if (value.isShield) {
            img = <img src={shielding} alt="屏蔽" />;
          } else if (value.useStatus && (value.openType === 1 || value.openType === 3)) {
            isStrangerTag = true;
            img = <img src={helper} alt="小助手" />;
          }
        }
        if (!img) {
          if ('relation' in value && value.relation) {
            if (
              value.relation === UserRelations.COLLEAGUE ||
              value.relation === UserRelations.FRIEND_AND_COLLEAGUE
            ) {
              img = <img src={colleague} alt="同事" />;
            } else if (value.relation === UserRelations.STRANGER) {
              isStrangerTag = true;
              img = <img src={stranger} alt="陌生人" />;
            }
          } else if ('groupType' in value) {
            switch (value.groupType) {
              case GroupTypes.INNER:
                img = <img src={inner} alt="内部" />;
                break;
              case GroupTypes.ALL_USER:
                img = <img src={full} alt="全员" />;
                break;
              case GroupTypes.DEPARTMENT:
                img = <img src={department} alt="部门" />;
                break;
              case GroupTypes.ANONYMOUS:
                img = <img src={anonymous} alt="匿名" />;
                break;
              case GroupTypes.CUSTOMER:
                img = <img src={customer} alt="客户" />;
                break;
              default:
            }
          }
        }
      }
    }
    return [img, isStrangerTag];
  }, [userId, value]);

  if (image) {
    return (
      <span
        {...props}
        className={classNames('ml-1', styles.tag, { [styles.stranger]: isStranger }, className)}
      >
        {image}
      </span>
    );
  }
  return null;
}

ChatTag.defaultProps = {
  userId: 0,
};

export default ChatTag;
