.spaceBox {
  :global {
    .ant-modal-title {
      font-weight: 600;
    }

    .ant-modal-body {
      padding: 0 20px;
    }

    .ant-modal-footer {
      padding: 20px;
    }

    .ant-input-textarea-show-count::after {
      position: relative;
      right: 16px;
      bottom: 26px;
    }
  }
}

.label {
  margin-bottom: 16px;
  font-weight: 600;
}

.name {
  display: flex;
  padding: 4px 0;
  margin-bottom: 20px;
  :global {
    .ant-input-affix-wrapper-focused {
      border-color: #006eff !important;
    }
    .ant-input-affix-wrapper:hover {
      border-color: #00c6ff !important;
    }
  }
}

.icon {
  width: 32px;
  height: 32px;
  padding: 4px;
  margin-right: 4px;
  border-radius: 6px;
  border: 1px solid #b1b3be;
  cursor: pointer;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.nameInput {
  flex: 1;
  padding: 0 16px;
}

.tip {
  margin-bottom: 16px;

  :global {
    .ant-input {
      padding: 5px 12px;
      height: 100px !important;
      resize: none;
    }

    .ant-input:focus {
      border-color: #006eff !important;
    }
    .ant-input:hover {
      // border-color: #441eff !important;
      // background: #006EFF;
      border-color: #00c6ff !important;
    }
  }
}
