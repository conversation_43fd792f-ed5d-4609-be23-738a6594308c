import { PropsWithChildren, useState } from 'react';
import { Modal, ModalProps, message } from '@echronos/antd';
import { isFunction, debounce } from 'lodash';
import { permSpaceCreateApi } from '@/apis';
import { v4 as uuid } from 'uuid';
import SpaceBox from './components/space-box';
import styles from './space-create.module.less';

export interface SpaceCreateProps extends ModalProps {
  onConfirm: () => void;
  /** 自定义的初始化文档数据 */
  customInitDocData?: { blockId: string; block: any[]; content: string[] };
}

function SpaceCreate({
  onConfirm,
  customInitDocData,
  ...props
}: PropsWithChildren<SpaceCreateProps>) {
  const [isDisabled, setIsDisabled] = useState(false);
  const [formValue, setFormValue] = useState({
    logo: '',
    name: '',
    description: '',
  });
  const [showEmoji, setShowEmoji] = useState(false);

  const onSubmit = debounce(() => {
    setIsDisabled(true);
    const blockId = uuid();
    const plcBlockId = uuid();

    // 文档初始化数据可以从外部传入, 也可以自己生成
    const initDocData = customInitDocData || {
      blockId,
      block: [
        {
          attrs: {
            blockId: plcBlockId,
            class: 'node-selectable',
            content: [],
            fullWidth: 'false',
            lineHeight: 24,
            padding: '0',
            margin: '0',
            parentId: '',
            rootBlockId: '',
            textAlign: 'left',
          },
          blockId: plcBlockId,
          content: [],
          parentId: blockId,
          type: 'paragraph',
        },
      ],
      content: [plcBlockId],
    };

    permSpaceCreateApi({
      ...formValue,
      ...initDocData,
      // head: {
      //   background: {
      //     transform: 50,
      //     url: `https://img.huahuabiz.com/web-document-img/${picture}`,
      //   },
      //   avatar: {
      //     type: 'icon',
      //     url: `https://img.huahuabiz.com/emoji/${emoji}`,
      //   },
      // },
    })
      .then(() => {
        message.success('创建成功');
        onConfirm();
        // @ts-ignore
        props.onClose();
      })
      .finally(() => {
        setIsDisabled(false);
      });
  }, 500);

  const onClose = () => {
    if (!showEmoji) {
      if (isFunction(props.onCancel)) {
        // @ts-ignore
        props.onCancel();
      }
    }
    setShowEmoji(false);
  };

  return (
    <Modal
      {...props}
      centered
      width={440}
      title="创建空间"
      wrapClassName={styles.modal}
      okButtonProps={{ disabled: !formValue.name.length || isDisabled }}
      okText="创建"
      onOk={onSubmit}
      onCancel={onClose}
      confirmLoading={isDisabled}
    >
      <SpaceBox onChange={setFormValue} onClickEmoji={setShowEmoji} />
    </Modal>
  );
}

SpaceCreate.defaultProps = {
  customInitDocData: {},
};

export default SpaceCreate;
