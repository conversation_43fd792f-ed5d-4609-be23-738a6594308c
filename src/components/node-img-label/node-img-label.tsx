import classNames from 'classnames';
import { mergeAttributes, Node, NodeViewProps } from '@tiptap/core';
import { ReactNodeViewRenderer, NodeViewWrapper } from '@tiptap/react';
import Suggestion from '@tiptap/suggestion';
import { getDefaultAttrs } from '@echronos/editor/dist/core';
import styles from './style.module.less';

declare module '@tiptap/core' {
  interface Commands {
    imgLabel: {
      /**
       * 插入提及
       */
      // eslint-disable-next-line no-unused-vars
      insertMention: (props: { [key: string]: any }) => void;
    };
  }
}

/** 更新方法 */
// eslint-disable-next-line no-unused-vars
const updateAttributesMap: { [key: string]: (attributes: Record<string, any>) => void } = {};

const NodeImgLabel = Node.create<any>({
  name: 'imgLabel',

  group: 'inline',

  inline: true,

  selectable: false,

  atom: true,

  addAttributes() {
    return {
      ...getDefaultAttrs(),
      id: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-id'),
        renderHTML: (attributes) => {
          if (!attributes.id) {
            return {};
          }

          return {
            'data-id': attributes.id,
          };
        },
      },

      type: {
        default: '',
      },

      imgLabel: {
        default: null,
        parseHTML: (element) => element.getAttribute('data-imgLabel'),
        renderHTML: (attributes) => {
          if (!attributes.imgLabel) {
            return {};
          }

          return {
            'data-imgLabel': attributes.imgLabel,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'imgLabel',
      },
    ];
  },

  renderHTML({ HTMLAttributes }: any) {
    return ['imgLabel', mergeAttributes(HTMLAttributes)];
  },

  addKeyboardShortcuts() {
    return {
      Backspace: () =>
        this.editor.commands.command(({ tr, state }) => {
          let isMention = false;
          const { selection } = state;
          const { empty, anchor } = selection;

          if (!empty) {
            return false;
          }

          // eslint-disable-next-line consistent-return
          state.doc.nodesBetween(anchor - 1, anchor, (node, pos) => {
            if (node.type.name === this.name) {
              isMention = true;
              tr.insertText('', pos, pos + node.nodeSize);

              return false;
            }
          });

          return isMention;
        }),
    };
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
      }),
    ];
  },

  addCommands() {
    return {
      insertMention:
        (props) =>
        ({ commands }) =>
          commands?.insertContentAt(1, {
            type: 'imgLabel',
            attrs: props,
          }),

      updateMention: (props: any) => () => {
        updateAttributesMap[props.type]({
          id: props.id,
          imgLabel: props.imgLabel,
        });
      },
    };
  },

  addNodeView() {
    return ReactNodeViewRenderer(({ node, updateAttributes }: NodeViewProps) => {
      const { blockId, imgLabel, id, type } = node.attrs;
      updateAttributesMap[type] = updateAttributes;

      window.console.log(id, 'id');

      return (
        <NodeViewWrapper
          className={classNames(styles.nodeImgLabelWrap, `node-${blockId}`)}
          as="span"
        >
          <span
            style={{ cursor: 'pointer' }}
            className={classNames(styles.nodeImgLabel, this.options.HTMLAttributes.class)}
          >
            {imgLabel}
          </span>
        </NodeViewWrapper>
      );
    });
  },
});

export default NodeImgLabel;
