import { Popover, PopoverProps } from 'antd';
import { useMemo, useState } from 'react';
import Icon from '@echronos/echos-icon';
import styles from './index.module.less';

interface DownSelectData {
  key: number;
  label: string;
}

interface DownSelectProps extends PopoverProps {
  items: DownSelectData[];
  currentKey: number;
  // eslint-disable-next-line no-unused-vars
  onChangeKey: (val: number, current: number) => void;
}

function DownSelect({ currentKey, items, placement, onChangeKey }: DownSelectProps) {
  const [visible, setVisible] = useState(false);

  const onSelectKey = (val: number) => {
    setVisible(false);
    onChangeKey(val, currentKey);
  };

  const currentInfo = useMemo(() => {
    return items.find((item) => item.key === currentKey);
  }, [currentKey, items]);

  const content = items.map((item) => {
    return (
      <div className={styles.item} onClick={() => onSelectKey(item.key)}>
        <span className={styles.itemText}>{item.label}</span>
        {currentKey === item.key && <Icon size="16" color="#000" name="yes_line" />}
      </div>
    );
  });

  return (
    <Popover
      trigger="hover"
      placement={placement}
      content={content}
      overlayClassName={styles.popover}
      visible={visible}
      onVisibleChange={(val) => setVisible(val)}
    >
      <div className={styles.box} onMouseEnter={() => setVisible(true)}>
        <span>{currentInfo?.label}</span>
        <Icon name="down_arrow_line" />
      </div>
    </Popover>
  );
}

export default DownSelect;
