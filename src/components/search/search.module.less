@import '@echronos/react/less/index';

.search {
  border-radius: 20px;
  // border-radius: 48px;

  :global {
    &,
    .ant-input {
      line-height: 18px;
    }

    .ant-input {
      font-size: @font-size-xs;
      border-radius: 0;
    }
  }
}

.default {
  border-radius: 48px !important;
  :global {
    &,
    .ant-input {
      border-color: @white;
    }
  }
}

.grey {
  :global {
    &,
    .ant-input {
      border-color: @background-color-base;
      background-color: @background-color-base;
    }
  }
}
