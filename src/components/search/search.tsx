/**
 * 搜索框组件
 */
import {
  ChangeEvent,
  ChangeEventH<PERSON>ler,
  CompositionEvent,
  CompositionEventHandler,
  forwardRef,
  KeyboardEventHandler,
} from 'react';
import { Input, InputProps, InputRef } from 'antd';
import classNames from 'classnames';
import { EMPTY_FN, nextTick } from '@echronos/core';
import { SearchOutlined } from '@echronos/icons';
import styles from './search.module.less';

export interface SearchProps extends InputProps {
  theme?: 'default' | 'grey';
  realTimeSearch?: boolean;
  onSearch?: MultipleParamsFn<[searchKey: string]>;
  defaultValue?: string;
  className?: string;
}

export type SearchInstance = InputRef;

const Search = forwardRef<SearchInstance, SearchProps>(
  ({ theme, realTimeSearch, className, onSearch, defaultValue, ...props }, ref) => {
    const emitSearch = (e: unknown) => {
      if (onSearch) {
        const { value } =
          (e as ChangeEvent<HTMLInputElement>).currentTarget ||
          (e as CompositionEvent<HTMLInputElement>).target;
        nextTick(() => {
          onSearch(value);
        });
      }
    };
    let handleChange: ChangeEventHandler<HTMLInputElement> | undefined;
    let handleCompositionEnd: CompositionEventHandler<HTMLInputElement> | undefined;
    let handleEnterSearch: KeyboardEventHandler<HTMLInputElement> | undefined;
    if (realTimeSearch) {
      handleChange = (e) => {
        if (e.nativeEvent && (e.nativeEvent as InputEvent).isComposing) {
          return;
        }
        emitSearch(e);
      };
      handleCompositionEnd = emitSearch;
    } else {
      handleEnterSearch = (e) => {
        if (e.key === 'Enter' || e.keyCode === 13) {
          emitSearch(e);
        }
      };
    }

    const classes = classNames(styles.search, theme && styles[theme], className);
    return (
      <Input
        {...props}
        ref={ref}
        maxLength={40}
        prefix={<SearchOutlined color="#999eb2" size={16} />}
        className={classes}
        onChange={handleChange}
        onCompositionEnd={handleCompositionEnd}
        onKeyUp={handleEnterSearch}
        defaultValue={defaultValue}
      />
    );
  }
);

Search.displayName = 'EchSearch';

Search.defaultProps = {
  theme: 'default',
  // eslint-disable-next-line react/default-props-match-prop-types
  allowClear: true,
  realTimeSearch: true,
  onSearch: EMPTY_FN,
  defaultValue: '',
  className: '',
};

export default Search;
