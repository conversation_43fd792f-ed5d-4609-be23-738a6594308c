import { HTMLAttributes } from 'react';
import { Spin } from 'antd';
import classNames from 'classnames';
import './index.less';

function SpinBox({
  loading,
  children,
  className,
  ...props
}: HTMLAttributes<HTMLElement> & { loading?: boolean }) {
  return (
    <div {...props} className={classNames('ech-spin-box', className)}>
      <Spin spinning={Boolean(loading)}>{children}</Spin>
    </div>
  );
}

SpinBox.defaultProps = {
  loading: true,
};

export default SpinBox;
