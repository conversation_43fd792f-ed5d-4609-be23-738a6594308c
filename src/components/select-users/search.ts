import { getDefaultAvatar } from '@echronos/core';

export interface SearchRelationMember {
  avatar: string;
  companyIds: string[] | null;
  dateMainId: number;
  id: number;
  isShield: 0 | 1 | boolean;
  memberId: number;
  nickname: string;
  realName: string;
  relation: number;
  remark: string;
  toUserId: number;
  userId: number;
  phone: string;
}

export interface SearchRelationCustomer {
  id: number;
  customerId: number;
  nickname: string;
  decisionFlag: 0 | 1 | boolean;
  phone: string;
  sex: number;
  avatar: string;
  remark: string;
  userId: number;
}

export interface SupplierContactItem {
  id: number;
  contactUserId: number;
  contactMemberId: number;
  customerId: number;
  userId: number;
  nickname: string;
  contactName: string;
  decisionFlag: number;
  phone: string;
  sex: number;
  avatar: string;
  remark: string;
  companyName: string;
  customerCompanyId: number;
}

export interface SearchRelationSupplier {
  customerId: number;
  companyId: number;
  customerName: string;
  customerCompanyId: number;
  isShare: boolean;
  contactList: SupplierContactItem[];
}

export interface SearchRelationCustomerCompany {
  accountPayable: string;
  accountReceivable: string;
  address: string;
  avatar: string;
  code: string;
  companyId: number;
  companyName: string;
  contact: string;
  contactList: SupplierContactItem[];
  customerCategory: number;
  customerCompanyId: number;
  customerCompanyName: string;
  customerName: string;
  customerType: number;
  id: number;
  name: string;
  phone: string;
  remark: string;
  source: number;
  status: number;
}

export interface SearchRelationUserResult {
  id: number;
  nickname: string;
  avatar: string;
  memberId: number;
  relation: number;
  isShield: 0 | 1 | boolean;
  companyIds: number[];
  customerId?: number;
  remark: string;
  realName: string;
  phone?: string;
}

export interface SearchRelationCustomerResult {
  id: number;
  nickname: string;
  avatar: string;
  remark: string;
  memberId: number;
  realName?: string;
  customerId: number;
  sex: number;
  phone: string;
  isDecision: 0 | 1 | boolean;
}

export interface SearchRelationSupplierResult {
  id: number;
  label: string;
  type: 'suppliers';
  companyId: number;
  members: Array<{
    id: number;
    label: string;
    avatar: string;
    userId: number;
    companyId: number;
    companyName: string;
    memberId: number;
    type: 'users';
  }>;
  userIds: number[];
  hideMore?: boolean;
  memberId?: number;
  avatar?: string;
  nickname?: string;
  realName?: string;
  remark?: string;
  phone?: string;
}

export interface SearchRelationCustomersCompanyResult {
  id: number;
  label: string;
  type: 'customers';
  companyId: number;
  members: Array<{
    id: number;
    label: string;
    avatar: string;
    userId: number;
    companyId: number;
    companyName: string;
    memberId: number;
    type: 'users';
  }>;
  userIds: number[];
  hideMore?: boolean;
  memberId?: number;
  avatar?: string;
  nickname?: string;
  realName?: string;
  remark?: string;
  phone?: string;
}

export type FormatSearchResultKey =
  | 'contactRespList'
  | 'lookingForList'
  | 'recentContactList'
  | 'strangerList'
  | 'colleagueSearches'
  | 'customerSupplierVOList'
  | 'customerVOList'
  | 'groupSearchVos';

const memberKeys: FormatSearchResultKey[] = [
  'lookingForList',
  'recentContactList',
  'strangerList',
  'colleagueSearches',
];

/**
 * 格式化搜索结果
 * @param res
 */
export function formatSearchResults(
  res: Record<
    FormatSearchResultKey,
    | SearchRelationMember[]
    | SearchRelationCustomer[]
    | SearchRelationSupplier[]
    | SearchRelationCustomerCompany[]
  >
) {
  const result: Record<
    string,
    | SearchRelationUserResult[]
    | SearchRelationCustomerResult[]
    | SearchRelationSupplierResult[]
    | SearchRelationCustomersCompanyResult[]
  > = {};
  memberKeys.forEach((key) => {
    if (res[key] && res[key].length !== 0) {
      result[key] = (res[key] as SearchRelationMember[]).map((item) => ({
        id: item.toUserId || item.id,
        nickname: item.nickname,
        realName: item.realName,
        avatar: item.avatar,
        memberId: item.memberId,
        relation: item.relation,
        isShield: item.isShield,
        companyIds: item.companyIds ? item.companyIds.map((id) => +id) : [],
        remark: item.remark,
        phone: item.phone,
      }));
    }
  });

  const customers = res.contactRespList as SearchRelationCustomer[];
  if (customers && customers.length !== 0) {
    result.contactRespList = customers.map((customer) => ({
      id: customer.userId,
      nickname: customer.nickname,
      avatar: customer.avatar,
      remark: customer.remark,
      memberId: customer.id,
      customerId: customer.customerId,
      sex: customer.sex,
      phone: customer.phone,
      isDecision: customer.decisionFlag,
    }));
  }

  const suppliers = res.customerSupplierVOList as SearchRelationSupplier[];
  if (suppliers && suppliers.length !== 0) {
    result.customerSupplierVOList = suppliers.map((supplier) => ({
      id: supplier.customerId,
      label: supplier.customerName,
      companyId: supplier.customerCompanyId,
      type: 'suppliers',
      userIds: supplier.contactList.map((contact) => contact.contactUserId),
      hideMore: !supplier.contactList.filter((contact) => !!contact.contactUserId).length,
      members: supplier.contactList
        .filter((contact) => !!contact.contactUserId)
        .map((contact) => ({
          id: contact.contactUserId,
          label: contact.contactName,
          avatar: contact.avatar || getDefaultAvatar(),
          memberId: contact.contactMemberId,
          userId: contact.contactUserId,
          companyId: contact.customerCompanyId || supplier.customerCompanyId,
          companyName: contact.companyName || supplier.customerName,
          type: 'users',
          phone: contact.phone,
        })),
    })) as SearchRelationSupplierResult[];
  }

  const customerCompanies = res.customerVOList as SearchRelationCustomerCompany[];
  if (customerCompanies && customerCompanies.length !== 0) {
    result.customerVOList = customerCompanies.map((customerCompany) => ({
      id: customerCompany.id,
      label: customerCompany.customerName,
      companyId: customerCompany.customerCompanyId,
      type: 'customers',
      userIds: customerCompany.contactList.map((contact) => contact.contactUserId),
      hideMore: !customerCompany.contactList.filter((contact) => !!contact.contactUserId).length,
      members: customerCompany.contactList
        .filter((contact) => !!contact.contactUserId)
        .map((contact) => ({
          id: contact.contactUserId,
          label: contact.contactName,
          avatar: contact.avatar || getDefaultAvatar(),
          memberId: contact.contactMemberId,
          userId: contact.contactUserId,
          companyId: contact.customerCompanyId || customerCompany.customerCompanyId,
          companyName: contact.companyName || customerCompany.customerName,
          type: 'users',
          phone: contact.phone,
        })),
    })) as SearchRelationCustomersCompanyResult[];
  }

  return result;
}
