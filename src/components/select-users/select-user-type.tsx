import { HTMLAttributes, useEffect, useMemo, useRef, useState } from 'react';
import { Avatar, Breadcrumb, Spin, Row, Col, Skeleton } from 'antd';
import { isArray } from '@echronos/core';
import findIndex from 'lodash/findIndex';
import pick from 'lodash/pick';
import { LeftOutlined, RightOutlined } from '@echronos/icons';
import type { CompanyOrganizationBreadcrumbResult } from '@/apis/utils/get-company-organizations';
import getLastLevelDepartments from '@/apis/utils/get-last-level-departments';
import biddingGetAllSuppliers, { ShareCompanyType } from '@/apis/utils/bidding-get-all-suppliers';
import getShareSupplierCompanies from '@/apis/utils/get-share-supplier-companies';
import getUserCompanyInfoList, {
  GetUserCompanyInfoListResult,
} from '@/apis/user/get-user-company-info-list';
import {
  allSelectTypes,
  getSelectTypeData,
  SelectTypeKeys,
  getOrganizations,
  SelectGroup,
  SelectedIds,
  cancelToken,
  HistoryItem,
  SelectOnlyDepartment,
  getCustomers,
  getCooperation,
  CustomerItem,
  getRole,
  getDistribution,
  DistributionItem,
  getSupplier,
  SupplierItem,
} from './util';
import type { Item } from './util';
import styles from './user-type.module.less';
import CompanyType from './user-company-type';
import SelectList, { SelectedHandler } from './select-list';
import FilterCustomer from './filter-customer';

export interface SelectUserTypeProps extends HTMLAttributes<HTMLDivElement> {
  selected: SelectedIds;
  selectedItems: Item[];
  defaults: number[];
  ignore?: boolean | SelectTypeKeys[];
  custom?: Item[] | SelectGroup[];
  insertItems?: SelectGroup[];
  isFirstShowUserName?: boolean;
  disables?: Item[];
  companyId?: number;
  showCompanyId?: number;
  onlyCompany?: boolean;
  allowCustomer?: boolean;
  customerCategory?: 1 | 2;
  allowRole?: boolean;
  roleType?: number;
  onlyDistribution?: number;
  allowDistribution?: boolean;
  onlyRole?: boolean;
  onlyDepartment?: SelectOnlyDepartment;
  onlyUser?: boolean;
  companyMemberRadio?: boolean;
  authCustomer?: boolean;
  authSupplier?: boolean;
  allowSupplier?: boolean;
  allowShareSupplier?: boolean;
  biddingMainSheetId?: number;
  supplierCategoryIds?: number[];
  supplierAppointType?: 0 | 1 | 2;
  onlySupplier?: boolean;
  selectSupplierCompany?: boolean;
  selectSupplierMember?: boolean;
  cooperation?: boolean;
  hideMore?: boolean;
  hideFilter?: boolean;
  showInactiveMember?: boolean;
  userIdKey?: string;
  userLabelKey?: string;
  distributionUserKey?: string;
  onSelected?: SelectedHandler;
  selectMemberType?: string;
}

const isHnc: boolean = import.meta.env.BIZ_APP_PLATFORM_NO === '1';

function SelectUserType({
  selected,
  selectedItems,
  ignore,
  custom,
  insertItems,
  defaults,
  disables,
  companyId,
  showCompanyId,
  onlyUser,
  onlyCompany,
  onlyDepartment,
  isFirstShowUserName,
  allowCustomer,
  customerCategory,
  allowRole,
  onlyRole,
  roleType,
  showInactiveMember,
  onlyDistribution,
  allowDistribution,
  authCustomer,
  authSupplier,
  allowSupplier,
  allowShareSupplier,
  biddingMainSheetId,
  companyMemberRadio,
  supplierCategoryIds,
  supplierAppointType,
  onlySupplier,
  selectSupplierCompany,
  selectSupplierMember,
  userIdKey,
  userLabelKey,
  distributionUserKey,
  cooperation,
  hideMore,
  hideFilter,
  onSelected,
  selectMemberType,
  ...props
}: SelectUserTypeProps) {
  const [companies, setCompanies] = useState([] as GetUserCompanyInfoListResult[]);
  const [loadCompanies, setLoadCompanies] = useState(false);
  const [current, setCurrent] = useState(null as null | HistoryItem);
  const [loading, setLoading] = useState(false);
  const [breadcrumbs, setBreadcrumbs] = useState([] as CompanyOrganizationBreadcrumbResult[]);
  const [list, setList] = useState(custom || []);
  const [error, setError] = useState('');
  const [height, setHeight] = useState(150);
  const [defaultValue, setDefaultValue] = useState({});
  const [shareSupplierItems, setShareSupplierItems] = useState<ShareCompanyType[]>([]);
  const [biddingSuppliers, setBiddingSuppliers] = useState<SupplierItem[]>([]);
  const headEl = useRef(null as unknown as HTMLDivElement);
  const selectTypes = useMemo(() => {
    if (custom || onlyCompany) {
      return [];
    }
    if (isArray(ignore) && ignore.length !== 0) {
      return allSelectTypes.filter((selectType) => ignore.indexOf(selectType.key) === -1);
    }
    return ignore ? [] : allSelectTypes;
  }, [custom, ignore, onlyCompany]);
  const history = useRef([] as HistoryItem[]);

  useEffect(() => {
    if (biddingMainSheetId) {
      biddingGetAllSuppliers({ mainSheetId: biddingMainSheetId }).then((res) => {
        setShareSupplierItems(res.shareCompanyList);
        setBiddingSuppliers(
          res.companyList.map((supplier) => ({
            id: supplier.customerId,
            label: supplier.companyName,
            hideMore: true,
            companyId: supplier.companyId,
            members: [],
            userIds: [],
            type: 'suppliers',
          }))
        );
      });
    }
  }, [biddingMainSheetId]);

  useEffect(() => {
    if (allowShareSupplier && !biddingMainSheetId) {
      getShareSupplierCompanies({ pageNo: 1, pageSize: 999 }).then((res) => {
        setShareSupplierItems(
          res.list.map((item) => ({
            shareCompanyId: item.companyId,
            shareCompanyName: item.companyName,
            shareCustomerList: [],
          }))
        );
      });
    }
  }, [biddingMainSheetId, allowShareSupplier]);

  useEffect(() => {
    if (onlyDepartment === 'last' && companyId) {
      getLastLevelDepartments(companyId).then((res) =>
        setList(
          res.list.map((depart) => ({
            id: depart.id,
            label: depart.orgName,
            userIds: [],
            members: [],
            type: 'departments',
          }))
        )
      );
    } else {
      setLoadCompanies(true);
      getUserCompanyInfoList({ type: onlyDistribution }, false)
        .then((res) => {
          if (!allowDistribution) {
            res.list.forEach((item) => {
              const nowItem = item;
              nowItem.orgList = item.orgList.filter((org) => org.orgName !== '分销组织');
            });
          }
          setCompanies(res.list || []);
        })
        .finally(() => {
          setLoadCompanies(false);
        });
    }
  }, [companyId, onlyDistribution, onlyDepartment, allowDistribution]);

  useEffect(() => {
    if (custom) return;
    setList([]);
    if (current) {
      setError('');
      cancelToken();
      setLoading(true);
      let promise;
      switch (current.key) {
        case 'friends':
        case 'groups':
        case 'chats':
        case 'strangers':
          setBreadcrumbs([]);
          promise = getSelectTypeData(current);
          break;
        default:
          if (current.props) {
            if (current.type === 'customer') {
              if ('id' in current.props) {
                const { id } = current.props;
                const index = list.findIndex((item) => 'id' in item && item.id === id);
                if (index !== -1) {
                  promise = Promise.resolve((list[index] as CustomerItem).members || []);
                }
              } else if (cooperation) {
                promise = getCooperation(1);
              } else {
                const params = current.props as Record<string, number>;
                if (customerCategory) {
                  params.customerCategory = customerCategory;
                }
                promise = getCustomers(params, authCustomer, userIdKey);
              }
            } else if (current.type === 'roles') {
              promise = getRole().then((res) => {
                if (roleType) {
                  return res.filter((role) => role.roleType === roleType);
                }
                return res;
              });
            } else if (current.type === 'supplier') {
              if ('id' in current.props) {
                const { id } = current.props;
                const index = list.findIndex((item) => 'id' in item && item.id === id);
                if (index !== -1) {
                  promise = Promise.resolve((list[index] as CustomerItem).members || []);
                }
              } else if (biddingMainSheetId) {
                promise = Promise.resolve(biddingSuppliers);
              } else {
                promise = getSupplier(
                  current.props.companyId as number,
                  selectSupplierCompany,
                  0,
                  authSupplier,
                  userIdKey,
                  supplierCategoryIds,
                  supplierAppointType
                );
              }
            } else if (current.type === 'shareSupplier') {
              if ('members' in current.props) {
                promise = Promise.resolve(current.props.members || []);
              } else {
                const index = findIndex(shareSupplierItems, [
                  'shareCompanyId',
                  current.props.companyId,
                ]);
                if (index !== -1) {
                  if (biddingMainSheetId) {
                    const resultList = shareSupplierItems[index].shareCustomerList.map(
                      (shareCustomer) => ({
                        id: shareCustomer.customerId,
                        label: shareCustomer.companyName,
                        hideMore: true,
                        companyId: shareCustomer.companyId,
                        members: [],
                        userIds: [],
                        type: 'suppliers',
                      })
                    );
                    promise = Promise.resolve(resultList);
                  } else {
                    promise = getSupplier(
                      current.props.companyId as number,
                      selectSupplierCompany,
                      1,
                      authSupplier,
                      userIdKey,
                      supplierCategoryIds,
                      supplierAppointType
                    );
                  }
                }
              }
            } else if (current.type === 'distributions') {
              if ('id' in current.props) {
                const { id } = current.props;
                const index = list.findIndex((item) => 'id' in item && item.id === id);
                if (index !== -1) {
                  promise = Promise.resolve((list[index] as DistributionItem).members || []);
                }
              } else {
                promise = getDistribution(
                  current.props as Record<string, number>,
                  distributionUserKey
                );
              }
            } else if (cooperation) {
              promise = getCooperation(2).then(
                (res) =>
                  // const bc = res.list.map((it) => ({
                  //   id: it.id,
                  //   orgName: it.label,
                  // }));
                  // setBreadcrumbs(bc);
                  res.list
              );
            } else {
              promise = getOrganizations(
                pick(current.props as Record<string, number>, ['id', 'companyId', 'isInvitation']),
                isFirstShowUserName as boolean,
                userIdKey,
                userLabelKey
              ).then((res) => {
                setBreadcrumbs(res.breadcrumbs);
                return res.list;
              });
            }
          }
      }
      if (promise) {
        promise
          .then((res) => {
            if (res) {
              const items = 'list' in res ? (res.list as Item[]) : res;
              if (insertItems) {
                items.forEach((item: SelectGroup) => {
                  const i = findIndex(insertItems, ['key', item.key]);
                  if (i !== -1) {
                    item.list.unshift(...insertItems[i].list);
                  }
                });
              }
              setList(items);
              if (items.length === 0) {
                setError('暂无数据');
              }
            }
          })
          .finally(() => {
            setLoading(false);
            setTimeout(() => {
              setHeight(470 - headEl.current.offsetHeight);
            });
          });
      } else {
        setLoading(false);
      }
    }
  }, [current]); // eslint-disable-line

  return (
    <div {...props}>
      {custom || onlyDepartment === 'last' ? (
        <SelectList
          list={list}
          defaults={defaults}
          onlyUser={onlyUser}
          onlyDepartment={onlyDepartment}
          selected={selected}
          selectedItems={selectedItems}
          onSelected={onSelected}
        />
      ) : (
        <ul className={styles.userType} style={{ display: current ? 'none' : '' }}>
          {selectTypes.map((selectType) => (
            <li key={selectType.key}>
              <div
                role="button"
                tabIndex={-1}
                title={selectType.label}
                className={styles.item}
                onClick={() => {
                  setCurrent(selectType);
                }}
              >
                <Avatar src={selectType.icon} size={32} alt={selectType.label} />
                <span className={styles.title}>{selectType.label}</span>
                <RightOutlined color="#999eb2" className="float-right" />
              </div>
            </li>
          ))}
          {!isHnc && companies && companies.length !== 0 ? (
            <li>
              <CompanyType
                companyId={companyId}
                showCompanyId={showCompanyId}
                companies={companies}
                allowCustomer={allowCustomer}
                allowRole={allowRole}
                allowSupplier={allowSupplier}
                allowShareSupplier={allowShareSupplier}
                shareSupplierItems={shareSupplierItems}
                onlySupplier={onlySupplier}
                onlyRole={onlyRole}
                cooperation={cooperation}
                onlyDistribution={onlyDistribution}
                showInactiveMember={showInactiveMember}
                onClick={(data) => {
                  const tmp = data.props;
                  setCurrent({
                    ...data,
                    key: `company_${tmp.companyId}_${tmp.id || ''}`,
                    label: data.type === 'customer' ? '我的客户' : data.label,
                  });
                }}
              />
            </li>
          ) : (
            loadCompanies && (
              <Row className={styles.body}>
                <Col span={24} className="px-4 pt-4">
                  <Skeleton loading active />
                </Col>
              </Row>
            )
          )}
        </ul>
      )}
      <div style={{ display: !current ? 'none' : '' }}>
        <Spin spinning={loading}>
          <div ref={headEl} className={styles.head}>
            <div style={{ display: 'flex' }}>
              <LeftOutlined
                size={18}
                className={styles.back}
                onClick={() => {
                  const item = history.current.pop();
                  if (item) {
                    setCurrent(item);
                  } else {
                    history.current = [];
                    setCurrent(null);
                  }
                }}
              />
              <h4 className={styles.headTitle}>{current ? current.label : ''}</h4>
            </div>
            {!authCustomer && current && current.type === 'customer' ? (
              <FilterCustomer
                hideFilter={hideFilter}
                value={defaultValue}
                style={{ display: history.current.length === 1 ? 'none' : '' }}
                onOk={(data) => {
                  setDefaultValue(data);
                  setLoading(true);
                  const params = {
                    ...data,
                    companyId: (current.props || {}).companyId as number,
                  } as Record<string, number>;
                  if (customerCategory) {
                    params.customerCategory = customerCategory;
                  }
                  getCustomers(params)
                    .then(({ list: items }) => {
                      setList(items);
                      if (items.length === 0) {
                        setError('暂无数据');
                      }
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                }}
              />
            ) : null}
            <Breadcrumb className={styles.breadcrumb}>
              {breadcrumbs.map((breadcrumb, index) => (
                <Breadcrumb.Item
                  key={breadcrumb.id}
                  onClick={() => {
                    if (current && index !== breadcrumbs.length - 1) {
                      const histories = history.current;
                      const idx = histories.findIndex(
                        (item) => item.props && item.props.id === breadcrumb.id
                      );
                      if (idx === -1) {
                        histories.push(current);
                        setCurrent({
                          ...current,
                          props: { id: breadcrumb.id },
                        });
                      } else {
                        setCurrent(histories[idx]);
                        history.current = histories.slice(0, idx);
                      }
                    }
                  }}
                >
                  {breadcrumb.orgName}
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          </div>
          <SelectList
            list={list}
            defaults={defaults}
            onlyUser={onlyUser}
            onlyDepartment={onlyDepartment}
            selected={selected}
            selectedItems={selectedItems}
            disables={disables}
            hideMore={hideMore}
            companyMemberRadio={companyMemberRadio}
            selectSupplierMember={selectSupplierMember}
            selectMemberType={selectMemberType}
            style={{ display: error ? 'none' : '', height: `${height}px` }}
            onSelected={onSelected}
            onMore={(item) => {
              if (current) {
                history.current.push(current);
                if (
                  item.type &&
                  [
                    'departments',
                    'customers',
                    'groups',
                    'roles',
                    'distributions',
                    'suppliers',
                  ].includes(item.type)
                ) {
                  setCurrent({
                    ...current,
                    props: item as unknown as Record<string, unknown>,
                  });
                }
              }
            }}
          />
          {error ? <div className={styles.error}>{error}</div> : null}
        </Spin>
      </div>
    </div>
  );
}

SelectUserType.defaultProps = {
  ignore: false,
  custom: undefined,
  insertItems: undefined,
  disables: [],
  companyId: undefined,
  showCompanyId: undefined,
  onlyCompany: false,
  onlyDepartment: false,
  isFirstShowUserName: false,
  allowCustomer: false,
  customerCategory: undefined,
  allowRole: false,
  roleType: undefined,
  allowDistribution: false,
  onlyDistribution: undefined,
  onlyRole: false,
  onlyUser: false,
  authCustomer: false,
  authSupplier: false,
  allowSupplier: false,
  companyMemberRadio: false,
  allowShareSupplier: false,
  biddingMainSheetId: undefined,
  supplierCategoryIds: undefined,
  supplierAppointType: undefined,
  onlySupplier: false,
  selectSupplierCompany: false,
  selectSupplierMember: false,
  showInactiveMember: false,
  distributionUserKey: 'userId',
  userIdKey: 'userId',
  userLabelKey: undefined,
  onSelected: undefined,
  cooperation: false,
  hideMore: false,
  hideFilter: false,
  selectMemberType: '',
};

export default SelectUserType;
