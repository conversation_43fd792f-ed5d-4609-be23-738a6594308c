import {
  forwardRef,
  MouseEvent,
  MouseEventHandler,
  PropsWithChildren,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Button, Col, message, Row } from 'antd';
import { isFunction, isNumber, isNil } from '@echronos/core';
import Search from '@/components/search';
import classNames from 'classnames';
import { useThrottleFn } from 'ahooks';
import styles from './select-users.module.less';
import SelectUserType from './select-user-type';
import SelectedBox from './selected-box';
import SearchResults, { SearchHandler } from './search-results';
import { SelectedHandler } from './select-list';
import type {
  Item,
  SelectedIds,
  SelectGroup,
  SelectItemKeys,
  SelectOnlyDepartment,
  SelectTypeKeys,
  SelectUsersValues,
} from './util';

export interface BodyProps {
  // 自定义数据列表
  custom?: Item[] | SelectGroup[];
  // 默认值/标签
  defaults?: (Item | number)[];
  // 是否取消默认标签的关闭按钮
  closable?: boolean;
  // 搜索占位符
  placeholder?: string;
  // 忽略的功能
  ignore?: boolean | SelectTypeKeys[];
  // 最少选择数量
  min?: number;
  // 最大选择数量
  max?: number;
  // 只允许选择的公司的公司ID
  companyId?: number;
  // 只允许选择的公司的公司ID，不作接口参数
  showCompanyId?: number;
  // 是否只允许选择公司
  onlyCompany?: boolean;
  // 是否值选择部门
  onlyDepartment?: SelectOnlyDepartment;
  // 是否只选择用户,搭配 onlyCompany 为只允许选择公司内的员工
  onlySelectUser?: boolean;
  // 是否优先展示用户名称
  isFirstShowUserName?: boolean;
  // 允许查看客户
  allowCustomer?: boolean;
  // 已认证客户
  authCustomer?: boolean;
  // 客户类型
  customerCategory?: 1 | 2;
  // 允许查看角色
  allowRole?: boolean;
  // 允许查看分销商
  allowDistribution?: boolean;
  // 是否只允许选择分销商
  onlyDistribution?: number;
  // 显示供应商
  allowSupplier?: boolean;
  // 已认证供应商
  authSupplier?: boolean;
  // 显示共享供应商
  allowShareSupplier?: boolean;
  // 只显示供应商
  onlySupplier?: boolean;
  // 只能选择供应商公司
  selectSupplierCompany?: boolean;
  // 只能选择供应商成员
  selectSupplierMember?: boolean;
  // 招投标ID
  biddingMainSheetId?: number;
  // 组织内，客户，供应商 一家公司只允许选一个人
  companyMemberRadio?: boolean;
  // 供应商用途
  supplierAppointType?: 0 | 1 | 2;
  // 供应商分类
  supplierCategoryIds?: number[];
  // 只允许选择角色
  onlyRole?: boolean;
  // 筛选角色类型
  roleType?: number;
  // 显示未激活成员
  showInactiveMember?: boolean;
  // 自定义插入数据列表
  insertItems?: SelectGroup[];
  // 自定义selectedBox选中的文本
  selectedBoxSelectedText?: string;
  // 是都开启确认按钮的禁用验证
  isDisabledConfirm?: boolean;
  // 分销人员选中的key：userId 或 memberId
  distributionUserKey?: string;
  // 组织架构\供应商人员key：userId 或 memberId
  userIdKey?: string;
  // 组织架构选择人员label
  userLabelKey?: string;
  // 搜索，过滤未认证的客户和供应商
  searchIsFilter?: 0 | 1 | null;
  // 选中项在tag重点label
  selectedTagLable?: string;
  // 禁用项
  disables?: Item[];
  // 我协作的客户
  cooperation?: boolean;
  // 隐藏下级按钮 false隐藏
  hideMore?: boolean;
  // 隐藏筛选
  hideFilter?: boolean;
  // 自定义内容区域
  content?: ReactNode | ReactNode[];
  // 自定义搜索方法
  search?: SearchHandler;
  showSearch?: boolean;
  onCancel?: MouseEventHandler<HTMLElement>;
  onOk?: MouseEventHandler<HTMLElement>;
  onChange?: MultipleParamsFn<[values: SelectUsersValues]>;
  selectMemberType?: string; // id 表示userid memberId 表示选用户id;
}

export interface BodyInstance {
  getValues: () => SelectedIds;
  getValue: MultipleParamsFn<[key: SelectItemKeys], number[]>;
  getList: () => Item[];
  getMembers: () => Item[];
  confirmLoading: MultipleParamsFn<[loading?: boolean]>;
  hasValue: () => boolean;
}

const Body = forwardRef<BodyInstance, PropsWithChildren<BodyProps>>(
  (
    {
      closable,
      defaults,
      placeholder,
      custom,
      onlySelectUser,
      companyId,
      showCompanyId,
      onlyCompany,
      isFirstShowUserName,
      onlyDepartment,
      allowCustomer,
      customerCategory,
      allowRole,
      allowDistribution,
      onlyDistribution,
      allowSupplier,
      allowShareSupplier,
      companyMemberRadio,
      authCustomer,
      authSupplier,
      onlySupplier,
      selectSupplierCompany,
      selectSupplierMember,
      biddingMainSheetId,
      supplierCategoryIds,
      supplierAppointType,
      onlyRole,
      roleType,
      showInactiveMember,
      selectedTagLable,
      insertItems,
      searchIsFilter,
      selectedBoxSelectedText,
      isDisabledConfirm,
      distributionUserKey,
      userIdKey,
      userLabelKey,
      disables,
      ignore,
      min,
      max,
      content,
      search,
      onCancel,
      onOk,
      onChange,
      showSearch,
      cooperation,
      hideMore,
      hideFilter,
      selectMemberType,
    },
    ref
  ) => {
    const defaultIds = useMemo(() => {
      if (defaults) {
        let items = defaults;
        if (closable) {
          items = items.filter((item) => isNumber(item) || item.closable === false);
        }
        return items.map((item) => (isNumber(item) ? item : item.id));
      }
      return [];
    }, [closable, defaults]);

    // 处理初始selected的值,支持type=departments部门选中,
    const createSelectedState = () => {
      const users: Array<number> = [];
      const departments: Array<number> = [];
      const roles: Array<number> = [];
      const distributions: Array<number> = [];
      const suppliers: Array<number> = [];
      const shareSuppliers: Array<number> = [];
      defaults?.forEach((defaultItem) => {
        if (isNumber(defaultItem)) {
          users.push(defaultItem);
        } else if (defaultItem.type === 'departments') {
          departments.push(defaultItem.id);
        } else if (defaultItem.type === 'roles') {
          roles.push(defaultItem.id);
        } else if (defaultItem.type === 'distributions') {
          distributions.push(defaultItem.id);
        } else if (defaultItem.type === 'suppliers') {
          suppliers.push(defaultItem.id);
        } else {
          users.push(defaultItem.id);
        }
      });
      return {
        users,
        departments,
        groups: [],
        customers: [],
        roles,
        distributions,
        suppliers,
        shareSuppliers,
      } as unknown as SelectedIds;
    };
    const [selected, setSelected] = useState(createSelectedState);

    const [selectedItems, setSelectedItems] = useState(
      (defaults || []).filter((item) => !isNumber(item)) as Item[]
    );
    const [searchKey, setSearchKey] = useState('');
    const selectedNum = useMemo(() => {
      if (onlyDepartment || selectSupplierCompany) {
        return selectedItems.length;
      }
      const ids = new Set();

      selectedItems.forEach((item) => {
        if ('userIds' in item) {
          item.userIds.forEach((id) => {
            ids.add(id);
          });
        } else {
          ids.add(item.id);
        }
        // 个人和部门都能选择，部门为空时也算1个数量
        if (item.type === 'departments') {
          ids.add(item.id);
        }
      });
      if (defaults) {
        defaults.forEach((item) => {
          if (isNumber(item)) {
            ids.delete(item);
          }
          if (selectMemberType === 'memberId') {
            // @ts-ignore
            if (isNumber(item.id)) {
              // @ts-ignore
              ids.delete(item.id);
            }
          }
        });
      }
      return ids.size;
    }, [defaults, onlyDepartment, selectSupplierCompany, selectedItems, selectMemberType]);
    const previous = useRef(null as null | { selected: SelectedIds; items: Item[]; num: number });
    const boxEl = useRef(null as unknown as HTMLDivElement);
    const [contentHeight, setContentHeight] = useState(0);
    const [loading, setLoading] = useState(false);
    const { run: onClickOk } = useThrottleFn((e: MouseEvent<HTMLButtonElement>) => {
      onOk?.(e);
    });

    const handleSelected: SelectedHandler = (data) => {
      previous.current = { selected, items: selectedItems, num: selectedNum };
      setSelected({
        ...selected,
        [data.key]: data.ids,
      });
      setSelectedItems(
        selectedItems
          .filter((item) => (item.type || 'users') !== data.key || data.ids.includes(item.id))
          .concat(data.items)
      );
    };

    useImperativeHandle(
      ref,
      () => ({
        getValues: () => selected,
        getValue: (key: SelectItemKeys) => selected[key],
        getList: () => selectedItems,
        getMembers: () => selectedItems.filter((item) => 'memberId' in item && item.memberId),
        confirmLoading: (isLoading = true) => {
          setLoading(isLoading);
        },
        hasValue: () =>
          selected.users.length !== 0 ||
          selected.groups.length !== 0 ||
          selected.departments.length !== 0,
      }),
      [selected, selectedItems]
    );

    useEffect(() => {
      let changed = true;
      if (max && max > 0) {
        setTimeout(() => {
          if (selectedNum > max) {
            changed = false;
            if (max === 1) {
              let selectKey: SelectItemKeys | undefined;
              setSelectedItems((prevState) => {
                const items = prevState.slice(prevState.length - 1);
                if (items[0]) {
                  selectKey = items[0].type || 'users';
                }
                return items;
              });
              setSelected((prevState) => {
                const state = {} as SelectedIds;
                (Object.keys(prevState) as SelectItemKeys[]).forEach((key) => {
                  state[key] = [];
                  if (key === selectKey) {
                    const id = prevState[key][prevState[key].length - 1];
                    if (!isNil(id)) {
                      state[key].push(id);
                    }
                  }
                });
                return state;
              });
            } else {
              if (previous.current) {
                setSelected(previous.current.selected);
                setSelectedItems(previous.current.items);
              }
              message.warn(`最多选择${max}${onlyDepartment ? '个部门' : '人'}`);
            }
          }
        });
      }
      if (changed && isFunction(onChange)) {
        onChange({ selected, items: selectedItems, num: selectedNum });
      }
      if (content) {
        const height = 466 - boxEl.current.offsetHeight;
        if (height !== contentHeight) {
          setContentHeight(height);
        }
      }
    }, [selectedNum, onlyDepartment]); // eslint-disable-line

    let disabledConfirm = false;
    if (
      (isDisabledConfirm && selectedNum === 0) ||
      (min && min > selectedNum) ||
      (max && max < selectedNum)
    ) {
      disabledConfirm = true;
    }

    return (
      <Row className={styles.body}>
        <Col span={12}>
          {showSearch && (
            <div className={styles.search}>
              <Search allowClear theme="grey" placeholder={placeholder} onSearch={setSearchKey} />
            </div>
          )}
          <div className={classNames(styles.results, { [styles.noneSearch]: !showSearch })}>
            <SelectUserType
              selected={selected}
              selectedItems={selectedItems}
              defaults={defaultIds}
              companyId={companyId}
              showCompanyId={showCompanyId}
              ignore={ignore}
              custom={custom}
              insertItems={insertItems}
              isFirstShowUserName={isFirstShowUserName}
              disables={disables}
              onlyCompany={onlyCompany}
              allowCustomer={allowCustomer}
              customerCategory={customerCategory}
              allowRole={allowRole}
              roleType={roleType}
              userIdKey={userIdKey}
              userLabelKey={userLabelKey}
              companyMemberRadio={companyMemberRadio}
              distributionUserKey={distributionUserKey}
              onlyDistribution={onlyDistribution}
              allowDistribution={allowDistribution}
              allowSupplier={allowSupplier}
              authCustomer={authCustomer}
              authSupplier={authSupplier}
              allowShareSupplier={allowShareSupplier}
              biddingMainSheetId={biddingMainSheetId}
              supplierCategoryIds={supplierCategoryIds}
              supplierAppointType={supplierAppointType}
              onlySupplier={onlySupplier}
              selectSupplierCompany={selectSupplierCompany}
              selectSupplierMember={selectSupplierMember}
              onlyRole={onlyRole}
              onlyDepartment={onlyDepartment}
              onlyUser={onlySelectUser}
              showInactiveMember={showInactiveMember}
              cooperation={cooperation}
              hideMore={hideMore}
              hideFilter={hideFilter}
              style={{ display: !searchKey ? '' : 'none' }}
              onSelected={handleSelected}
              selectMemberType={selectMemberType}
            />
            <SearchResults
              selected={selected}
              selectedItems={selectedItems}
              defaults={defaultIds}
              disables={disables}
              companyId={companyId}
              userIdKey={userIdKey}
              userLabelKey={userLabelKey}
              companyMemberRadio={companyMemberRadio}
              searchIsFilter={searchIsFilter}
              searchKey={searchKey}
              cooperation={cooperation}
              selectSupplierMember={selectSupplierMember}
              search={search}
              onlyUser={onlySelectUser}
              style={{ display: !searchKey ? 'none' : '' }}
              onSelected={handleSelected}
              selectMemberType={selectMemberType}
            />
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.operation}>
            <SelectedBox
              ref={boxEl}
              closable={closable}
              defaults={defaultIds}
              selected={selectedItems}
              selectedTagLable={selectedTagLable}
              selectedText={selectedBoxSelectedText}
              style={{ maxHeight: content ? '218px' : '468px' }}
              className={content ? styles.selectedBorder : ''}
              onClose={(item) => {
                const key = item.type || 'users';
                handleSelected({
                  key,
                  ids: selected[key].filter((id) => id !== item.id),
                  items: [],
                });
              }}
            />
            {content ? (
              <div className={styles.contentWrap} style={{ height: `${contentHeight}px` }}>
                <div className={styles.content}>{content}</div>
              </div>
            ) : null}
          </div>
          <div className={styles.btnBox}>
            <Button shape="round" className={styles.btn} onClick={onCancel}>
              取消
            </Button>
            <Button
              disabled={disabledConfirm}
              shape="round"
              type="primary"
              loading={loading}
              className={styles.btn}
              onClick={onClickOk}
            >
              确定
            </Button>
          </div>
        </Col>
      </Row>
    );
  }
);

Body.defaultProps = {
  defaults: undefined,
  closable: true,
  placeholder: '搜索',
  custom: undefined,
  onlySelectUser: undefined,
  ignore: false,
  min: undefined,
  max: undefined,
  companyId: undefined,
  showCompanyId: undefined,
  onlyCompany: false,
  onlyDepartment: false,
  isFirstShowUserName: false,
  allowCustomer: false,
  authCustomer: false,
  customerCategory: undefined,
  allowRole: false,
  allowDistribution: false,
  onlyDistribution: undefined,
  onlyRole: false,
  companyMemberRadio: false,
  roleType: undefined,
  insertItems: undefined,
  selectedBoxSelectedText: '个会话',
  isDisabledConfirm: true,
  distributionUserKey: 'userId',
  userIdKey: 'userId',
  userLabelKey: undefined,
  selectedTagLable: '',
  disables: [],
  allowSupplier: false,
  authSupplier: false,
  allowShareSupplier: false,
  supplierCategoryIds: undefined,
  supplierAppointType: undefined,
  onlySupplier: false,
  selectSupplierCompany: false,
  selectSupplierMember: false,
  biddingMainSheetId: undefined,
  showInactiveMember: false,
  searchIsFilter: 0,
  content: undefined,
  search: undefined,
  onCancel: undefined,
  onOk: undefined,
  onChange: undefined,
  showSearch: true,
  cooperation: false,
  hideMore: false,
  hideFilter: false,
  selectMemberType: '',
};

export default Body;
