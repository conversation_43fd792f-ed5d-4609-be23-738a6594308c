@import '@echronos/react/less/index';
@import './select-users.module';

.userType {
  margin: 0;
  padding: 0;
  list-style: none;
}

.head {
  padding: 12px 16px;
}

.back {
  display: inline-block;
  line-height: 18px;
  padding-right: 8px;
  cursor: pointer;
  vertical-align: top;
}

.headTitle {
  font-size: @font-size-base;
  display: inline-block;
  line-height: 18px;
  margin-bottom: 0;
  vertical-align: top;
}

.breadcrumb {
  color: @text-color;
  font-size: @font-size-xs;
  margin-top: 4px;
  padding-left: 26px;

  :global {
    span {
      .ant-breadcrumb-link {
        cursor: pointer;
      }

      &:last-child {
        .ant-breadcrumb-link {
          cursor: default;
        }
      }
    }
  }
}

.item {
  line-height: 32px;
  padding: 12px 16px;
  cursor: pointer;

  //&:hover {
  //  background-color: #f5f6f7;
  //}
}

.panel {
  line-height: 32px;
  border-top: 1px solid #f5f6f7;

  &:last-child {
    border-bottom: 1px solid #f5f6f7 !important;
  }

  :global {
    .ant-collapse-content {
      > .ant-collapse-content-box {
        padding: 0 0 8px !important;
      }
    }

    .ant-collapse-header {
      padding-left: 16px !important;
      cursor: default !important;
    }
  }
}

.title {
  .text-overflow();

  display: inline-block;
  max-width: 75%;
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  vertical-align: middle;
}

.icon {
  width: 14px;
  height: 14px;
  margin-top: 9px;
  transition-duration: 0.3s;
}

.active {
  transform: rotateZ(180deg);
}

.itemTree {
  font-size: @font-size-xs;
  line-height: 18px;
  margin-bottom: 4px;
  padding: 4px 16px 4px 54px;
  cursor: pointer;

  &:hover {
    background-color: #f5f6f7;
  }
}

.treeIcon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
  vertical-align: top;
}
