import { forwardRef } from 'react';
import { Col, Modal, ModalProps, Row, Skeleton } from 'antd';
import { loadable, popupComponent } from '@echronos/react';
import { isFunction } from '@echronos/core';
import type { BodyInstance, BodyProps } from './body';
import styles from './select-users.module.less';

export type SelectUsersProps = BodyProps &
  Pick<ModalProps, 'zIndex' | 'maskClosable' | 'title' | 'afterClose'> & {
    // eslint-disable-next-line react/require-default-props
    visible?: boolean;
  };

export type SelectUsersInstance = BodyInstance;

export type SelectUsersConfig = SelectUsersProps & {
  onConfirm?: MultipleParamsFn<
    [instance: SelectUsersInstance, close: () => void],
    void | Promise<void>
  >;
};

const SelectUsersBody = loadable(() => import('./body'), {
  fallback: (
    <Row className={styles.body}>
      <Col span={12} className="px-4 pt-4">
        <Skeleton loading active />
      </Col>
      <Col span={12} className="px-4 pt-4">
        <Skeleton loading active />
      </Col>
    </Row>
  ),
});

const SelectUsers = forwardRef<SelectUsersInstance, SelectUsersProps>(
  ({ visible, title, maskClosable, afterClose, zIndex, ...props }, ref) => (
    <Modal
      centered
      visible={visible}
      title={title}
      zIndex={zIndex}
      afterClose={afterClose}
      maskClosable={maskClosable}
      width={730}
      footer={null}
      closable={false}
      className={styles.selectUsers}
      onCancel={props.onCancel}
    >
      <SelectUsersBody {...props} ref={ref} />
    </Modal>
  )
);

/**
 * 选择用人员
 * @param config
 */
function selectUsers(config: SelectUsersConfig) {
  let selectInstance: SelectUsersInstance;

  function ref(ins: SelectUsersInstance) {
    selectInstance = ins;
  }

  // @ts-ignore
  const popupInstance = popupComponent<SelectUsersProps>(SelectUsers, config, {
    ref,
    render: (render, conf, instance) => {
      const close = instance.destroy;
      const currentConfig = conf;
      currentConfig.onOk = (e) => {
        const { onConfirm, onOk } = config;
        if (isFunction(onOk)) {
          onOk(e);
        }
        if (isFunction(onConfirm)) {
          const promise = onConfirm(selectInstance, close);
          if (promise) {
            promise.finally(close);
          }
        } else {
          close();
        }
      };
      render(currentConfig);

      return instance;
    },
  });

  return { destroy: popupInstance.destroy };
}

export default selectUsers;
