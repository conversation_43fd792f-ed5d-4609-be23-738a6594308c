/**
 * 选择用户所属的分类
 */
import { CSSProperties } from 'react';
import { Avatar } from 'antd';
import { isFunction } from '@echronos/core';
import { RightOutlined } from '@echronos/icons';
import type { GetUserCompanyInfoListResult } from '@/apis/user/get-user-company-info-list';
import styles from './user-type.module.less';
import CompanyType from './user-company-type';
import friends from './img/friends.png';
import groups from './img/groups.png';
import chats from './img/chats.png';
import strangers from './img/strangers.png';

export type UserTypeKeys = 'friends' | 'groups' | 'chats' | 'strangers';

interface UserTypeItem {
  key: UserTypeKeys;
  label: string;
  icon: string;
}

export interface UserTypeProps {
  ignore?: UserTypeKeys[];
  companies?: GetUserCompanyInfoListResult[];
  // eslint-disable-next-line no-unused-vars
  onClick?: (key: string) => void;

  style?: CSSProperties;
}

const userTypes: UserTypeItem[] = [
  { key: 'friends', label: '我的好友', icon: friends },
  { key: 'groups', label: '我的群聊', icon: groups },
  { key: 'chats', label: '最近聊天', icon: chats },
  { key: 'strangers', label: '陌生人', icon: strangers },
];

function UserType({ ignore, companies, onClick, ...props }: UserTypeProps) {
  let showTypes: UserTypeItem[];
  if (ignore && ignore.length !== 0) {
    showTypes = userTypes.filter((item) => ignore.includes(item.key));
  } else {
    showTypes = userTypes;
  }

  return (
    <ul {...props} className={styles.userType}>
      {showTypes.map((item) => (
        <li key={item.key}>
          <div
            role="button"
            tabIndex={-1}
            title={item.label}
            className={styles.item}
            onClick={() => {
              if (isFunction(onClick)) {
                onClick(item.key);
              }
            }}
          >
            <Avatar src={item.icon} size={32} alt={item.label} />
            <span className={styles.title}>{item.label}</span>
            <RightOutlined color="#999eb2" className="float-right" />
          </div>
        </li>
      ))}
      {companies && companies.length !== 0 ? (
        <li>
          <CompanyType companies={companies} />
        </li>
      ) : null}
    </ul>
  );
}

UserType.defaultProps = {
  ignore: undefined,
  companies: [],
  onClick: undefined,
  style: undefined,
};

export default UserType;
