@import '@echronos/react/less/index';

.selectUsers {
  :global {
    .ant-modal-content,
    .ant-modal-header {
      margin-bottom: 16px;
      padding-bottom: 0;
      background-color: #fff;
      border-radius: 18px;
    }

    .ant-modal-body {
      padding: 0 20px;
      border-top: 1px solid #f5f6fa;
    }
  }
}

.body {
  height: 530px;
  border-radius: @border-radius-base;
  background-color: #fff;
  overflow: hidden;

  &:global {
    > .ant-col {
      &:first-child {
        border-right: 1px solid #f5f6f7;
      }
    }
  }
}

.search {
  padding: 16px 16px 8px;

  :global {
    .ant-input-affix-wrapper {
      border: 1px solid #f5f6fa;
      background: #f5f6fa;
      border-radius: 20px;

      &:active {
        border: 1px solid #00c6ff;
      }

      &:hover {
        border: 1px solid #00c6ff;
      }

      .ant-input {
        background: #f5f6fa;
      }
    }
  }
}

.results,
.contentWrap {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.results {
  height: 470px;
}

.noneSearch {
  height: 530px;
}

.operation,
.selected {
  display: flex;
  flex-flow: column;
  position: relative;
}

.selectedBorder {
  &::after {
    content: '';
    display: block;
    height: 1px;
    position: absolute;
    right: 16px;
    bottom: 0;
    left: 16px;
    background-color: #f5f6fa;
  }
}

.operation {
  height: 466px;
}

.content {
  padding: 0 16px;
}

.selected {
  .flex-column();

  padding-bottom: 16px;
}

.selectedText {
  color: @text-color-secondary;
  line-height: 20px;
  margin-bottom: 0;
  padding: 16px;
}

.selectedList {
  margin-bottom: 0;
  padding: 0 12px;
  overflow-y: auto;
  list-style: none;
  -webkit-overflow-scrolling: touch;
}

.selectedItem {
  margin: 0 4px 8px !important;
}

.selectedItemLabel {
  max-width: 280px !important;
}

.btnBox {
  padding: 16px;
  background-color: #fff;
  text-align: right;
}

.btn {
  min-width: 74px;

  & + & {
    margin-left: 16px;
  }
}

.searchBox {
  min-height: 470px;
  position: relative;
}

.searchTitle {
  color: @text-color-secondary;
  font-size: @font-size-xs;
  padding: 12px 16px;
}

.error {
  color: #b1b3be;
  width: 100%;
  padding-top: 190px;
  text-align: center;
}

.head {
  padding: 12px 16px;
}

.back {
  display: inline-block;
  line-height: 18px;
  padding-right: 8px;
  cursor: pointer;
  vertical-align: top;
}

.headTitle {
  font-size: @font-size-base;
  display: inline-block;
  line-height: 18px;
  margin-bottom: 0;
  vertical-align: top;
}
