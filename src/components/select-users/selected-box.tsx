import { forwardRef, HTMLAttributes } from 'react';
import { Tag, Typography } from 'antd';
import classNames from 'classnames';
import { Item } from './util';
import styles from './select-users.module.less';

interface SelectedBoxBaseProps {
  defaults: number[];
  closable?: boolean;
  selectedText?: string; // 已选文案,不传默认是 '个会话'
  selectedTagLable?: string;
}

export interface SelectedBoxProps extends SelectedBoxBaseProps, HTMLAttributes<HTMLSpanElement> {
  selected: Item[];
  onClose: MultipleParamsFn<[item: Item]>;
}

const { Text } = Typography;

const SelectedBox = forwardRef<HTMLDivElement, SelectedBoxProps>(
  (
    { selected, className, style, onClose, closable, defaults, selectedText, selectedTagLable },
    ref
  ) => (
    <div ref={ref} className={classNames(styles.selected, className)} style={style}>
      <p className={styles.selectedText}>
        已选：{selected.length}
        {selectedText}
      </p>
      <div className={styles.selectedList}>
        {selected.map((tag) => (
          <Tag
            key={`${tag.type || 'users'}_${tag.id}_${tag.label}`}
            closable={
              !(
                tag.closable === false ||
                (closable === false &&
                  (tag.type === 'users' || tag.type === 'departments' || !tag.type) &&
                  defaults.includes(tag.id))
              )
            }
            className={styles.selectedItem}
            onClose={() => {
              onClose(tag);
            }}
          >
            <Text ellipsis className={styles.selectedItemLabel}>
              {selectedTagLable === 'companyName-label'
                ? // @ts-ignore
                  `${tag.companyName}-${tag.label}`
                : tag.label}
            </Text>
          </Tag>
        ))}
      </div>
    </div>
  )
);

SelectedBox.defaultProps = {
  closable: true,
  selectedText: '个会话',
  selectedTagLable: '',
};

export default SelectedBox;
