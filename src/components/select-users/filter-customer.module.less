.icon {
  float: right;
  cursor: pointer;
}

.modal {
  :global {
    .ant-modal-body {
      padding: 0;
    }

    .ant-tabs-nav {
      padding-left: 24px;
    }

    .ant-tabs-tab {
      padding: 19px 0;
    }
  }
}

.pane {
  height: 278px;
  padding: 0 16px;
  overflow-y: auto;
}

.btn {
  min-width: 95px;
  margin: 0 4px 12px 8px;
  text-align: center;
  border-radius: 16px !important;
  border-width: 1px !important;

  &:not(:first-child) {
    &::before {
      display: none;
    }
  }
}

.group {
  margin-right: -4px;
  margin-left: -8px;
}

.title {
  padding: 0 0 12px 8px;
}

.button {
  display: inline-block;
  margin: 0 4px 12px 8px;

  :global {
    .ant-checkbox {
      display: none;

      & + span {
        display: inline-block;
        min-width: 95px;
        line-height: 30px;
        border-radius: 16px;
        border: 1px solid #cccdd6;
        text-align: center;
      }
    }

    .ant-checkbox-checked {
      & + span {
        color: #fff;
        border-color: #f90400;
        background-color: #f90400;
      }
    }
  }
}
