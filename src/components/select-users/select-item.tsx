import { Avatar, Checkbox } from 'antd';
import classNames from 'classnames';
import { isFunction } from '@echronos/core';
import { RightOutlined } from '@echronos/icons';
import ChatTag from '@/components/chat-tag';
import { Item, SelectItemHandler } from './util';
import styles from './select-item.module.less';

export interface SelectItemProps {
  item: Item;
  selected: number[];
  disabled?: boolean;
  isItem?: boolean;
  isMore?: boolean;
  hideMore?: boolean;
  onMore?: SelectItemHandler;
}

function SelectItem({
  item,
  selected,
  disabled,
  isItem,
  isMore,
  onMore,
  hideMore,
}: SelectItemProps) {
  let more;
  if (isMore) {
    if (
      item.type === 'departments' ||
      item.type === 'customers' ||
      item.type === 'distributions' ||
      item.type === 'suppliers'
    ) {
      const isDisabled = selected && selected.includes(item.id);
      const moreClass = classNames(styles.moreTree, { [styles.disabledTree]: isDisabled });
      more = !hideMore && (
        <span
          role="button"
          tabIndex={-1}
          className={moreClass}
          onClick={(e) => {
            e.preventDefault();
            if (!isDisabled && isFunction(onMore)) {
              onMore(item);
            }
          }}
        >
          下级
        </span>
      );
    } else if (item.type === 'groups') {
      more = <RightOutlined size={16} color="#606266" className={styles.moreIcon} />;
    }
  }
  let avatar;
  if ('avatar' in item && item.avatar) {
    avatar = <Avatar src={item.avatar} size={32} className={styles.avatar} />;
  }

  const content = (
    <div
      role="button"
      tabIndex={-1}
      title={item.label}
      className={styles.content}
      onClick={(e) => {
        if (item.type === 'groups') {
          e.preventDefault();
          if (isFunction(onMore) && !selected.includes(item.id)) {
            onMore(item);
          }
        }
      }}
    >
      {avatar}
      <div className={styles.info}>
        <div style={{ display: 'flex' }}>
          <span className={styles.label}>{item.label}</span>
          {item.type !== 'departments' ? (
            <ChatTag value={item as { relation: number }} className={styles.tag} />
          ) : (
            <span className={styles.memberNum}>（{item.members.length}人）</span>
          )}
        </div>
        <p className={styles.description}>{item.description}</p>
      </div>
      {more}
    </div>
  );

  if (isItem) {
    return (
      <div className={styles.item}>
        <span className="empty-block" />
        <span>{content}</span>
      </div>
    );
  }

  return (
    <Checkbox value={item.id} disabled={disabled} className={styles.item}>
      {content}
    </Checkbox>
  );
}

SelectItem.defaultProps = {
  disabled: false,
  isItem: false,
  isMore: true,
  onMore: undefined,
  hideMore: true,
};

export default SelectItem;
