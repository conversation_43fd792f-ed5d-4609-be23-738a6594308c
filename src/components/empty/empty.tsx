import { CSSProperties, HTMLAttributes, PropsWithChildren, ReactNode } from 'react';
import classNames from 'classnames';
import isString from 'lodash/isString';
import styles from './empty.module.less';
import contentEmpty from './img/content.png';
import shopEmpty from './img/shop.png';
import orderEmpty from './img/order.png';
import addressEmpty from './img/address.png';
import commentEmpty from './img/comment.png';
import infoEmpty from './img/info.png';
import cartEmpty from './img/cart.png';
import networkEmpty from './img/network.png';
import searchEmpty from './img/search.png';

export type EmptyType =
  | 'content'
  | 'shop'
  | 'order'
  | 'address'
  | 'comment'
  | 'info'
  | 'cart'
  | 'network'
  | 'search';

export interface EmptyProps extends HTMLAttributes<HTMLDivElement> {
  type?: EmptyType;
  image?: ReactNode;
  message?: ReactNode;
  description?: ReactNode;
  imageStyle?: CSSProperties;
}

const defaultEmpty: Record<EmptyType, PropsWithChildren<EmptyProps>> = {
  content: { image: contentEmpty, message: '暂无相关内容' },
  shop: { image: shopEmpty, message: '暂无相关店铺信息' },
  order: {
    image: orderEmpty,
    message: '一个订单都没有哦',
    description: '给自己定个小目标，先下一单。',
  },
  address: {
    image: addressEmpty,
    message: '一个地址都没有哦',
    description: '填好地址，给自己少一个麻烦事吧~',
  },
  comment: { image: commentEmpty, message: '诶？还没有人给您评论' },
  info: { image: infoEmpty, message: '这里没有相关信息哟', description: '返回上一级页面逛一逛吧~' },
  cart: {
    image: cartEmpty,
    message: '购物车空空如也',
    // description: '来“商机”发现让您惊喜的商品哦',
  },
  network: { image: networkEmpty, message: '诶？网络开小差了' },
  search: { image: searchEmpty, message: '暂无数据', description: '换个筛选条件再试试' },
};

function Empty(props: PropsWithChildren<EmptyProps>) {
  // eslint-disable-next-line react/destructuring-assignment
  const emptyData = defaultEmpty[props.type as EmptyType];
  const {
    image = emptyData.image,
    imageStyle,
    message = emptyData.message,
    description = emptyData.description,
    children = emptyData.children,
    className,
    ...divProps
  } = props;

  const classes = classNames(styles.empty, className);
  return (
    <div {...divProps} className={classes}>
      <span className={styles.image} style={imageStyle}>
        {isString(image) ? <img src={image} alt="" /> : image}
      </span>
      {message ? <p className={styles.message}>{message}</p> : null}
      {description ? <p className={styles.description}>{description}</p> : null}
      {children ? <div className={styles.footer}>{children}</div> : null}
    </div>
  );
}

Empty.displayName = 'EchEmpty';
Empty.defaultProps = {
  type: 'content',
  image: undefined,
  message: undefined,
  description: undefined,
  imageStyle: undefined,
};

export default Empty;
