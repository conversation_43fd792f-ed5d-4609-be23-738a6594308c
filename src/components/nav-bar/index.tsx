import { PropsWithChildren, useContext, useMemo } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import SplitterContext from '@/utils/SplitterContext';
import Icon from '@echronos/echos-icon';
import { Tooltip } from '@echronos/antd';
import classNames from 'classnames';
import SiderProvider from '@/utils/SiderProvider';
import styles from './index.module.less';

interface NavBarProp {
  fullBtn?: boolean;
  closedBtn?: boolean;
}

/** 用于右测操作栏 */
function NavBar({ children, fullBtn, closedBtn }: PropsWithChildren<NavBarProp>) {
  const location = useLocation();
  const { fullScreenRight, isFullRight, fullScreenLeft, closeRight, setLastUrlCache } =
    useContext(SplitterContext);
  const { Collapsed, CanFloatSider } = useContext(SiderProvider);
  const [params] = useSearchParams();
  const isShowSpaceBtn = useMemo(
    () =>
      params.get('type') === 'group' ||
      (params.get('target') &&
        params.get('target') !== null &&
        (params.get('target') === '/user-center' ||
          (params.get('target') as string).indexOf('/m/m-distribution/full-network-statistic') >
            -1 ||
          params.get('target') === '/user-center?type=edit')),
    [params]
  );

  const handleCollapsed = () => {
    fullScreenRight();
  };

  const handleCollapsedClosed = () => {
    fullScreenLeft();
    closeRight();
    setLastUrlCache(location.pathname.substring(location.pathname.lastIndexOf('/') + 1));
  };

  return (
    <div
      className={classNames(styles['navbar-container'], {
        [styles.offset]: Collapsed || CanFloatSider,
      })}
    >
      {fullBtn && !isShowSpaceBtn && (
        <div role="button" tabIndex={-1} className="insider-logo_btnIcon" onClick={handleCollapsed}>
          <Tooltip
            title={!isFullRight ? '放大' : '缩小'}
            color="white"
            overlayInnerStyle={{ color: 'black' }}
            placement="bottom"
          >
            {!isFullRight && <Icon tabIndex={-1} size="18px" role="button" name="enlarge" />}
            {isFullRight && <Icon tabIndex={-1} size="18px" role="button" name="put_it_away" />}
          </Tooltip>
        </div>
      )}
      {children}
      {closedBtn && !isShowSpaceBtn && (
        <div
          role="button"
          tabIndex={-1}
          className="insider-logo_btnIcon"
          onClick={handleCollapsedClosed}
        >
          <Tooltip
            title="关闭"
            color="white"
            overlayInnerStyle={{ color: 'black' }}
            placement="bottom"
          >
            <Icon tabIndex={-1} size="18px" role="button" name="close_line" />
          </Tooltip>
        </div>
      )}
    </div>
  );
}

NavBar.defaultProps = {
  fullBtn: true,
  closedBtn: true,
};

export default NavBar;
