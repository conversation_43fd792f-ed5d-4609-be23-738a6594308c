import { Modal, ModalFuncProps } from 'antd';
import classNames from 'classnames';
import styles from './modal.module.less';

const confirm = function confirmFn({ className, ...props }: ModalFuncProps) {
  const classes = classNames(styles.echModalConfirm, className);
  return Modal.confirm({
    width: 312,
    centered: true,
    cancelText: '取消',
    okText: '确定',
    ...props,
    className: classes,
  });
};

export default confirm;
