// import Icon from '@echronos/echos-icon';
// import { openURL } from '@echronos/core';
import { useRequest } from 'ahooks';
// import Icon from '@echronos/echos-icon';
import {
  //  Tooltip,
  Space,
} from '@echronos/antd';
import { useState, useEffect, MouseEvent } from 'react';
// import getIframeToParentChannel from '@/service/iframe-to-parent-channel';
// import { useEditorState } from '@echronos/editor/dist/core';
import { SharePublish } from '@/containers';
// import { getPageData, useEditorState } from '@echronos/editor/dist/core';
import useDocStore from '@/store/useDocStore';
import getPagePer from '@/apis/site-manager/get-page-per';
import Oluser from './components/ol-user';
import ShowUser from './components/show-user';
import styles from './index.module.less';

function DocBreadcrumb() {
  /** 控制发布按钮的显示与隐藏 */
  const [permCode, setPermCode] = useState<any>([]);

  // const [isFullScrren, setIsFullScrren] = useState(false);

  const { onlineUsers, updateIsShowPublish, docBlockId } = useDocStore();
  // const { editor } = useEditorState();

  // const { editable } = useEditorState();

  // /** 触发提问 */
  // const handleTriggerAsk = (e: MouseEvent) => {
  //   e.stopPropagation();
  //   editor?.commands.askAi('outBtn');
  // };

  /** 预览文档 */
  // const handleViewDoc = () => {
  //   const page = getPageData();
  //   // @ts-ignore
  //   const link = `${import.meta.env.BIZ_FRONT_SITE}/doc/${page.pageId}`;
  //   openURL(link);
  // };

  /** 获取权限 */
  const { run } = useRequest(getPagePer, {
    manual: true,
    onSuccess: (res) => {
      setPermCode(res.perCodes);
    },
  });

  /** 发布文档 */
  const handlePublish = (e: MouseEvent) => {
    e.stopPropagation();
    // 显示发布笔记
    updateIsShowPublish(true);
    // updateViewDoc(true);
  };

  // const handleCollapsed = () => {
  //   if (!isFullScrren) {
  //     getIframeToParentChannel().emit('drawerFull');
  //   } else {
  //     getIframeToParentChannel().emit('drawerUnFull');
  //   }
  //   setIsFullScrren(!isFullScrren);
  // };

  // const handleClosed = () => {
  //   getIframeToParentChannel().emit('drawerClose');
  // };

  useEffect(() => {
    run({ blockId: docBlockId });
  }, [docBlockId, run]);

  return (
    <div className={styles['doc-breadcrumb-container']}>
      {/* <div className={styles['collapse-box']}>
        <div
          role="button"
          tabIndex={-1}
          style={{ marginRight: '5px', cursor: 'pointer' }}
          onClick={handleCollapsed}
        >
          <Tooltip
            title={!isFullScrren ? '放大' : '缩小'}
            color="white"
            overlayInnerStyle={{ color: 'black' }}
            placement="bottom"
          >
            {!isFullScrren && <Icon tabIndex={-1} size="18px" role="button" name="enlarge" />}
            {isFullScrren && <Icon tabIndex={-1} size="18px" role="button" name="put_it_away" />}
          </Tooltip>
        </div>
        <div role="button" tabIndex={-1} style={{ cursor: 'pointer' }} onClick={handleClosed}>
          <Tooltip
            title="关闭"
            color="white"
            overlayInnerStyle={{ color: 'black' }}
            placement="bottom"
          >
            <Icon tabIndex={-1} size="26px" role="button" name="close_line" />
          </Tooltip>
        </div>
      </div> */}

      <Space size={14}>
        {/* 在线用户 */}
        {onlineUsers.length ? (
          <div className={styles['user-icons']}>
            <ShowUser />
            {/* 仅保留下拉查看当前页面协作者 */}
            <Oluser />
          </div>
        ) : null}

        <SharePublish>
          <div className={styles['hov-hovItem']}>
            <span>分享</span>
          </div>
        </SharePublish>
        {/* 发布按钮 */}
        {(permCode.includes('BS_001_001') || permCode.includes('BS_001_002')) && (
          <div onClick={handlePublish} className={styles.publish}>
            发布
          </div>
        )}

        {/* ai */}
        {/* <div className={styles['hov-hovItem']} onClick={handleTriggerAsk}>
          <img
            alt=""
            style={{ width: '20px', height: '20px' }}
            src="https://img.huahuabiz.com/user_files/20241015/1728986040261215.png"
          />
        </div> */}
        {/* {editable && !isMobile && (
          <Tooltip placement="top" title="预览">
            <div className={styles['hov-hovItem']}>
              <Icon
                size={22}
                color="#000"
                name="hide_view_eyes"
                onClick={handleViewDoc}
                style={{ cursor: 'pointer' }}
              />
            </div>
          </Tooltip>
        )} */}

        {/* {editable && (
        <HeaderMoreHandle
          editor={editor}
          pageAttrs={pageAttrs}
          onPageAttrsChange={onPageAttrsChange}
        />
      )} */}
      </Space>
    </div>
  );
}

export default DocBreadcrumb;
