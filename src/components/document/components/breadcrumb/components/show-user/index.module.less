@import '@/styles/mixins.less';

.wrap {
  .list {
    gap: 10px;
    .flex-layout(@alignItems:center;);

    .item {
      cursor: pointer;
      width: 28px;
      height: 28px;
      line-height: 28px;
      border-radius: 14px;
      text-align: center;
      color: #fff;
      position: relative;

      &.active {
        &::before {
          content: '';
          width: 16px;
          height: 2px;
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          background: #004cff;
        }
      }

      .avatar {
        width: 28px;
        height: 28px;
        border-radius: 14px;
        opacity: 1;
        display: block;
      }
    }
  }
}

:global {
  .online-user-popover {
    .ant-popover-arrow {
      display: block !important;

      .ant-popover-arrow-content:before {
        background: #040919;
      }
    }

    .ant-popover-inner {
      background: #040919;

      .ant-popover-inner-content {
        color: #fff;
        text-align: center;

        .name {
          line-height: 22px;
          margin-bottom: 4px;
        }

        .role {
          color: rgba(255, 255, 255, 0.5);
        }
      }
    }
  }
}
