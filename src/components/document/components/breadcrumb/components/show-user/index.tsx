import classNames from 'classnames';
import { Popover } from '@echronos/antd';
import { useMemo } from 'react';
import { useDocStore, User, useUserInfoStore } from '@/store';
import styles from './index.module.less';

// 用户颜色
const userNameColorArr = ['#6600FF', '#004DFF', '#DE00C0', '#E18C3E', '#7100D3'];

function ShowUser() {
  const { onlineUsers, setOnlineUsers } = useDocStore();

  const { user } = useUserInfoStore();
  // @ts-expect-error todo
  const { userId } = user.user;

  const getFistLetter = (str: string) => {
    return str ? str[0] : '';
  };

  const renderList = useMemo(() => {
    const arr = onlineUsers.filter((item) => {
      return item.id !== userId;
    });
    return arr.slice(0, 5).reverse();
  }, [onlineUsers, userId]);

  // 锚点
  const handleAnchor = (_user: User) => {
    document
      .querySelector(`.user-${_user.id}`)
      ?.scrollIntoView({ behavior: 'smooth', block: 'start' });

    setOnlineUsers(
      onlineUsers.map((item) => {
        return {
          ...item,
          active: _user.id === item.id,
        };
      })
    );
  };

  return (
    <div className={styles.wrap}>
      <div className={styles.list}>
        {renderList.map((_user, index) => {
          return (
            <Popover
              content={
                <div>
                  <p className="name">{_user.nickname}</p>
                  {/* <p className="role">管理员</p> */}
                </div>
              }
              overlayClassName="online-user-popover"
            >
              <div
                key={_user.id}
                onClick={() => handleAnchor(_user)}
                style={{ backgroundColor: _user.avatar ? '#fff' : userNameColorArr[index] }}
                className={classNames(styles.item, _user.active && styles.active)}
              >
                {_user.avatar ? (
                  <img className={styles.avatar} src={_user.avatar} alt="" />
                ) : (
                  getFistLetter(_user.nickname)
                )}
              </div>
            </Popover>
          );
        })}
      </div>
    </div>
  );
}

export default ShowUser;
