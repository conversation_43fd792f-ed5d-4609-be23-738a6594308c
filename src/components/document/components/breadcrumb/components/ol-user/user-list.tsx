import { useDocStore, User } from '@/store';
import styles from './user-list.module.less';

function Userlist() {
  const { onlineUsers, setOnlineUsers } = useDocStore();

  // 锚点
  const handleAnchor = (user: User) => {
    document
      .querySelector(`.user-${user.id}`)
      ?.scrollIntoView({ behavior: 'smooth', block: 'start' });

    setOnlineUsers(
      onlineUsers.map((item) => {
        return {
          ...item,
          active: user.id === item.id,
        };
      })
    );
  };
  return (
    <div className={styles.wrap}>
      <div className={styles.title}>全部协作者</div>
      <div className={styles.list}>
        {onlineUsers.map((user) => {
          return (
            <div key={user.id} className={styles.item} onClick={() => handleAnchor(user)}>
              <img className={styles.avatar} src={user.avatar} alt="" />
              <div className={styles.name}>{user.nickname}</div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default Userlist;
