@import '@/styles/mixins.less';

.wrap {
  cursor: pointer;
  height: 28px;
  border-radius: 315px;
  padding: 2px;
  min-width: 55px;
  // gap: 8px;
  background: rgba(245, 246, 250, 0.5);
  .flex-layout(@alignItems: center; );
  justify-content: space-around;
  .total {
    margin: 0 4px 0 8px;
  }

  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 12px;
    opacity: 1;
  }

  .arrow {
    cursor: pointer;
  }
}

:global {
  .ol-user-popover {
    .ant-popover-inner-content {
      background-color: #040919;
      border-radius: 5px;
      padding: 0;
      margin-top: -8px;
    }

    .ant-popover-arrow {
      .ant-popover-arrow-content:before {
        background-color: #040919;
      }
    }
  }
}
