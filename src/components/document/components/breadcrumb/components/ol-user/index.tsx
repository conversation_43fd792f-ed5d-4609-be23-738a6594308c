import { Popover } from '@echronos/antd';
import { DownOutlined } from '@ant-design/icons';
import { useDocStore, useUserInfoStore } from '@/store';
import UserListModal from './user-list';
import styles from './index.module.less';

function Oluser() {
  const { onlineUsers } = useDocStore();
  const { user } = useUserInfoStore();

  // @ts-expect-error todo
  const { userId } = user.user;

  const self = onlineUsers.find((item) => item.id === userId);

  return (
    <Popover overlayClassName="ol-user-popover" content={<UserListModal />}>
      <div className={styles.wrap}>
        <img className={styles.avatar} src={self?.avatar} alt="" />
        {onlineUsers.length >= 4 ? <div className={styles.total}>{onlineUsers.length}</div> : null}
        <div className={styles.arrow}>
          <DownOutlined size={20} />
        </div>
      </div>
    </Popover>
  );
}

export default Oluser;
