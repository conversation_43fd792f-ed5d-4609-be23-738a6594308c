@import '@/styles/mixins.less';

.wrap {
  width: 200px;
  max-height: 234px;
  padding: 12px 0;
  // padding: 12px 0;
  background: #040919;
  border-radius: 8px;
  overflow-y: auto;

  .title {
    line-height: 22px;
    color: rgba(255, 255, 255, 0.5);
    padding: 4px 12px 8px 12px;
  }

  .list {
    &:last-child {
      margin-bottom: 0;
    }
    .item {
      cursor: pointer;
      padding: 8px 12px;
      position: relative;
      .flex-layout(@alignItems:center;);

      &:hover {
        background: rgba(255, 255, 255, 0.1);

        &::before {
          content: '';
          display: block;
          width: 1px;
          height: 16px;
          background: #05d380;
          position: absolute;
          left: 0;
          top: 13px;
        }
      }

      .avatar {
        width: 26px;
        height: 26px;
        border-radius: 13px;
        margin-right: 8px;
      }

      .name {
        color: #fff;
        line-height: 22px;
      }
    }
  }
}
