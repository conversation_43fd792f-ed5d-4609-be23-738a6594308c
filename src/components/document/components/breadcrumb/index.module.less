.doc-breadcrumb-container {
  width: 100%;
  height: 48px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  -webkit-backdrop-filter: blur(100px);
  backdrop-filter: blur(100px);
  background: rgba(100%, 100%, 100%, 0.6);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 11;
  padding-right: 20px;

  .collapse-box {
    display: flex;
    align-items: center;
  }

  .user-icons {
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 10px;
  }

  .hov-hovItem {
    cursor: pointer;
    height: 26px;
    line-height: 26px;
    border-radius: 8px;
    box-sizing: border-box;
    user-select: none;
    display: flex;
    align-items: center;
  }

  .publish {
    cursor: pointer;
    border-radius: 2px;
  }
}
