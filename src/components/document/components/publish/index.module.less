.publish-warap {
  width: 100%;
  position: sticky;
  bottom: 0;
}

.publish-box {
  max-width: var(--el-max-w);
  margin: 0 auto;
  z-index: 99;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  padding: 0 15px 10px 15px;
  background: #ffffff;
  box-sizing: border-box;
  border: 1px solid #dadbe0;
  box-shadow: 2px 4px 12px 0px rgba(2, 9, 58, 0.08);
  border-radius: 6px;

  .publish-box-left {
    padding-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    .publishBtn {
      :global {
        display: flex;
        // position: relative;
        justify-content: center;
        align-items: center;
        width: 64px;
        height: 30px;
        border-radius: 4px;
        background: #ffffff;
        border: 1px solid #b1b3be;
        cursor: pointer;
        margin-right: 12px;
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        -khtml-user-select: none;
      }

      &:hover {
        background: #d9eeff;
        border: 1px solid #008cff;
        color: #008cff;
      }
    }

    .publishBtnimg {
      :global {
        display: flex;
        // position: relative;
        justify-content: center;
        align-items: center;
        width: 64px;
        height: 30px;
        border-radius: 4px;
        background: #ffffff;
        border: 1px solid #b1b3be;
        cursor: pointer;
        margin-right: 12px;
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        -khtml-user-select: none;
      }

      &:hover {
        background: #d9eeff;
        border: 1px solid #008cff;
        color: #008cff;
      }
    }

    .publishBtnimg:hover .iconPath {
      background: #d9eeff;
      border: 1px solid #008cff;
      color: #008cff;
      fill: #008cff;
    }
  }

  .publish-box-tip {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;

    .publish-checkbox {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .publish-tooltip {
      height: 14px;
    }
  }

  .publish-box-right {
    padding-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;

    .companypublishbtn {
      :global {
        width: 64px;
        height: 28px;
        border-radius: 10px;
        line-height: 28px;
        text-align: center;
        font-family: PingFangSC-Medium, sans-serif;
        font-size: 14px;
        margin-right: 12px;
        cursor: pointer;
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        -khtml-user-select: none;
        background: linear-gradient(251deg, #00c6ff 1%, #008cff 100%);
        color: #ffffff;
      }
    }

    .companycancelbtn {
      :global {
        width: 64px;
        height: 28px;
        border-radius: 10px;
        background: #f3f3f3;
        color: #040919;
        line-height: 28px;
        text-align: center;
        font-family: PingFangSC-Medium, sans-serif;
        font-size: 14px;
        cursor: pointer;
        user-select: none;
        -moz-user-select: none;
        -webkit-user-select: none;
        -ms-user-select: none;
        -khtml-user-select: none;
      }
    }
  }
}

.addGoods {
  height: 30px;
  padding: 0 15px;
  border: 1px solid #b1b3be;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 27px;

  .plusIcon {
    margin-right: 4px;
  }

  &:hover {
    background: #d9eeff;
    border: 1px solid #008cff;
    color: #008cff;
  }
}

.selectTips {
  font-size: 12px;
  color: #c4c4c4;
}
