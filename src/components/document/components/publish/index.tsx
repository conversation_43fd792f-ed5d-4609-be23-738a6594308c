import { v4 as uuid } from 'uuid';
import Icon from '@echronos/echos-icon';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import { Dispatch, SetStateAction, useState } from 'react';
import { useEditorState } from '@echronos/editor/dist/core';
import { Editor as TiptapEditor, JSONContent } from '@tiptap/core';
import { Checkbox, Dropdown, Menu, Tooltip, message } from '@echronos/antd';
import { getContentIds, getBlocks } from '@echronos/editor/dist/extension-on-content-update-all';
import useDocStore from '@/store/useDocStore';
import { PublishDocPageParams, publishDocPage } from '@/apis';
import styles from './index.module.less';

// 使用正则表达式匹配 [节点类型:值] 的模式
const parse = (str: string, regex: RegExp): string[] => {
  let match;
  const topicValues: string[] = [];

  // 使用 exec 方法循环查找所有匹配的内容
  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(str)) !== null) {
    // match[1] 是捕获的内容部分
    topicValues.push(match[1]);
  }

  return topicValues;
};

const selectGoodsNode = [
  {
    key: '0',
    label: (
      <>
        <p>自营商品</p>
        <p className={styles.selectTips}>选择自己上架的商品</p>
      </>
    ),
  },
  {
    key: '1',
    label: (
      <>
        <p>分销商品</p>
        <p className={styles.selectTips}>选择其他商家的商品</p>
      </>
    ),
  },
];

interface PublishProps {
  blockId: string;
  editor: TiptapEditor | null;
  setSelfType: Dispatch<SetStateAction<boolean>>;
  setGoodsDranwer: Dispatch<SetStateAction<boolean>>;
}

function Publish({ blockId, editor, setSelfType, setGoodsDranwer }: PublishProps) {
  const { docHeadInfo, updateIsShowPublish } = useDocStore();
  const { content } = useEditorState();

  /** 是否智能分发 */
  const [intelligentChecked, setIntelligentChecked] = useState(true);

  // 复选框按钮改变事件
  const onCheckboxChange = (e: CheckboxChangeEvent) => {
    setIntelligentChecked(e.target.checked);
  };

  // 添加商品下拉菜单
  const selectGoods = (
    <Menu
      items={selectGoodsNode}
      onClick={(e) => {
        setSelfType(e.key === '0');
        setGoodsDranwer(true);
      }}
    />
  );

  // 获取编辑器里的数据
  const getEditorData = () => {
    // 特殊处理节点的文本
    const editorText1 = editor?.getText({
      textSerializers: {
        // 格式化图片
        image: (data) => {
          const src = data.node.attrs.src.split('https://img.huahuabiz.com/')[1];
          return `[image:${src}]`;
        },
        // 格式化话题
        mention: (data) => {
          return `[topic:${data.node.attrs.id}]`;
        },
      },
    }) as string;
    // 获取编辑器内图片
    // const images = parse(editorText1, /\[image:([^\]]+)\]/g);

    // 话题 id
    const topicIds = parse(editorText1, /\[topic:([^\]]+)\]/g);

    // 用于展示的纯文本
    const editorText2 = editor?.getText({
      textSerializers: {
        // 格式化话题
        mention: (data) => {
          return `#${data.node.attrs.label}`;
        },
      },
    }) as string;

    const pageBlockId = uuid();
    const editorContent = editor?.getJSON().content as JSONContent[];

    // json
    const jsonContent = {
      blockId: pageBlockId, // 块ID
      type: 'page',
      parentId: blockId,
      docHeadInfo,
      block: getBlocks(JSON.parse(JSON.stringify(editorContent))),
      content: getContentIds(editorContent),
    };
    const objs = {
      jsonContent: JSON.stringify(jsonContent),
      topicIds: topicIds.map(Number),
      content: editorText2,
    };
    return objs;
  };

  // 发布按钮点击事件
  const onPublishNote = () => {
    const editorData = getEditorData();
    const coverImageList = [];
    if (docHeadInfo.background.url) {
      coverImageList.push(docHeadInfo.background.url);
    }
    const params: PublishDocPageParams = {
      blockId: content?.attrs?.blockId,
      title: docHeadInfo.title,
      coverImageList,
      isIntelligent: intelligentChecked ? 1 : 0,
      ...editorData,
    };
    publishDocPage(params).then(
      () => {
        message.success('发布成功');
        updateIsShowPublish(false);
      },
      (error: any) => {
        message.warning(error);
      }
    );
  };

  // 触发话题
  const handleTopic = (e: any) => {
    e.stopPropagation();
    editor?.commands.insertContent(' #');
  };

  // 触发表情
  const handleEmoji = (e: any) => {
    e.stopPropagation();
    editor?.commands.selectEmoji('cursor', editor?.state.selection);
  };

  return (
    <div className={styles['publish-warap']}>
      <div className={styles['publish-box']}>
        <div className={styles['publish-box-left']}>
          <div className={styles.publishBtn} onClick={handleTopic}>
            <text style={{ margin: '0px 5px 0 0', fontSize: 15 }}>#</text>
            <text>话题</text>
          </div>
          <div className={styles.publishBtnimg} onClick={handleEmoji}>
            <Icon name="expression_line" />
            <text>表情</text>
          </div>
          <Dropdown overlay={selectGoods}>
            <div className={styles.addGoods}>
              <Icon name="add_line" size={15} className={styles.plusIcon} />
              添加商品
            </div>
          </Dropdown>
        </div>
        <div className={styles['publish-box-tip']}>
          <div className={styles['publish-checkbox']}>
            <Checkbox checked={intelligentChecked} onChange={onCheckboxChange}>
              <span>全网智能分发</span>
            </Checkbox>
          </div>
          <div className={styles['publish-tooltip']}>
            <Tooltip placement="topRight" title="开启后，系统将按话题投放到接收此话题的各平台">
              <span>
                <Icon name="info_line" />
              </span>
            </Tooltip>
          </div>
        </div>
        <div className={styles['publish-box-right']}>
          <div className={styles.companypublishbtn} onClick={onPublishNote}>
            发布
          </div>
          <div className={styles.companycancelbtn} onClick={() => updateIsShowPublish(false)}>
            取消
          </div>
        </div>
      </div>
    </div>
  );
}

export default Publish;
