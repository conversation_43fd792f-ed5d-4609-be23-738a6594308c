import { Drawer } from 'antd';
import { v4 as uuid } from 'uuid';
import { Editor as TiptapEditor } from '@tiptap/core';
import { SelectGoodsDranwer } from '@echronos/editor/dist/node-goods-distribution';
import docJSONAddBlockId from '@/pages/chat/components/chat-item/components/edit-pop-content/docJSONAddBlockId';

/** 文本类节点 */
const textLike = ['heading1', 'heading2', 'heading3', 'paragraph'];

interface SelectGoodsProps {
  editor: TiptapEditor | null;
  goodsDranwer: boolean;
  selfType: boolean;
  onClose: () => void;
}

function SelectGoods({ editor, goodsDranwer, selfType, onClose }: SelectGoodsProps) {
  /** 插入分销商品卡片 */
  const onAddShowCard = (attrs: any) => {
    if (editor) {
      // 页面 id
      const bid = uuid();
      // 单独插入商品
      const goodsCard = [
        {
          type: 'goodsDistributionCard',
          attrs: {
            params: attrs.params,
          },
        },
      ];
      // 补全字段
      const docContentJSON2 = docJSONAddBlockId(goodsCard, bid);
      const { state } = editor;
      const { lastChild } = state.doc;
      const pnodes = Array.from(document.querySelectorAll('.node-selectable'));
      const focusedEl = pnodes.find((item) => {
        return (
          // @ts-ignore
          item.attributes?.blockid?.value === state.selection.$head.parent.attrs.blockId
        );
      });
      setTimeout(() => {
        // focusedEl：判断聚焦点是否文本
        if (focusedEl) {
          // 文本执行插入
          editor.commands.insertContentAt(
            {
              from: state.selection.from as number,
              to: state.selection.to as number,
            },
            docContentJSON2
          ); // 判断是否
        } else if (lastChild?.childCount === 0 && textLike.includes(lastChild.type.name)) {
          editor.commands.insertContentAt(state.doc.content.size - 1, docContentJSON2);
        } else {
          editor.commands.insertContentAt(state.doc.content.size, docContentJSON2);
        }
        editor.commands.focus('end');
      }, 500);
    }
  };

  return (
    <>
      <SelectGoodsDranwer
        visible={goodsDranwer}
        self={selfType}
        updateAttributes={onAddShowCard}
        onClose={onClose}
      />
      {/* 引入antd样式，误删 */}
      <Drawer visible={false} />
    </>
  );
}

export default SelectGoods;
