import React, { useEffect, useRef } from 'react';
import { DocumentHeadCover } from '@echronos/millet-ui';
import type { DocumentHeadCoverData } from '@echronos/millet-ui';
import { useEditorState, generateUniqueId, getPageData } from '@echronos/editor/dist/core';
import useDocStore from '@/store/useDocStore';
import styles from './index.module.less';

export const toObj = (data: DocumentHeadCoverData) => {
  return JSON.parse(JSON.stringify(data)) as DocumentHeadCoverData;
};
function Head({ onUpdate }: { onUpdate: (data: DocumentHeadCoverData) => void }) {
  const { editable, editor } = useEditorState();
  const { docHeadInfo } = useDocStore();

  const DHRef = useRef<any>(null);

  // 处理回车
  const handleEnter = () => {
    const selection = window.getSelection() as Selection;
    const startIndex = selection.anchorOffset;
    const endIndex = selection.focusOffset;
    const textVal = selection.anchorNode?.textContent;
    const splitStr = textVal?.substring(endIndex);
    const restStr = textVal?.substring(0, startIndex) as string;

    // 更新输入框
    DHRef?.current?.updateData({
      ...docHeadInfo,
      title: restStr,
    });

    // 将光标后的内容插入到文档第一行
    const options = splitStr
      ? {
          type: 'paragraph',
          attrs: {
            blockId: generateUniqueId(),
            spaceId: getPageData().spaceId,
          },
          content: [
            {
              text: splitStr,
              type: 'text',
            },
          ],
        }
      : {
          type: 'paragraph',
          attrs: {
            blockId: generateUniqueId(),
            spaceId: getPageData().spaceId,
          },
        };

    editor?.chain().insertContentAt(0, options).focus().run();

    // 协同数据
    onUpdate({ ...docHeadInfo, title: restStr });
  };

  useEffect(() => {
    const data = toObj(docHeadInfo);
    console.log('[log 62]: ', 62);
    // console.log('数据同步本地---ffffff', data);
    DHRef?.current?.updateData(data);
  }, [docHeadInfo]);
  return (
    <div
      className={styles['doc-header-wrap']}
      style={{ pointerEvents: editable ? 'unset' : 'none' }}
    >
      <DocumentHeadCover
        isEdit
        ref={DHRef}
        defaultData={docHeadInfo}
        onSubmit={onUpdate}
        onEnter={handleEnter}
      />
    </div>
  );
}

export default React.memo(Head);
