import { debounce } from 'lodash';
import { useRequest } from 'ahooks';
import { Spin, message } from '@echronos/antd';
import classNames from 'classnames';
import Icon from '@echronos/echos-icon';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { TopicItem, getTopicCommonList, addTopicCommon } from '@/apis';

import './index.less';

interface Ref {
  onKeyDown: (event: any) => void;
}

interface MentionListProps {
  items: any[];
  onSelectItem: (props: any) => void;
}

const MentionList = forwardRef<Ref, MentionListProps>(({ items, onSelectItem }, ref) => {
  const [total, setTotal] = useState(0);
  const [pageNo, setPageNo] = useState(1);
  const [loaded, setLoaded] = useState(false);
  const [list, setList] = useState<TopicItem[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const listWrapRef = useRef<HTMLDivElement>(null); // 滚动区域 ref

  // 列表请求
  const { run, loading } = useRequest(getTopicCommonList, {
    manual: true,
    debounceWait: 300,
    onSuccess(res) {
      setList([...list, ...(res.list || [])]);
      setTotal(res.pagination.count);
      setLoaded(true);
    },
    onError() {},
  });

  // 添加话题
  const { run: run2 } = useRequest(addTopicCommon, {
    manual: true,
    onSuccess(res) {
      // @ts-expect-error todo
      onSelectItem({ id: res.id, label: items[0] });
    },
    onError() {
      message.warning('添加失败, 请稍后重试');
    },
  });

  const selectItem = (index: number) => {
    const findItem = list.find((_item, ind) => ind === index);
    if (findItem) {
      onSelectItem({ id: findItem.id, label: findItem.name });
      setSelectedIndex(index);
    }
  };

  // 文本高亮
  const textHighLight = (str: string) => {
    // eslint-disable-next-line no-param-reassign
    str = `#${str}`;
    // 使用全局正则表达式匹配所有出现的 items[0]
    const regex = new RegExp(items[0], 'g');
    return str.replace(regex, `<span style="color: #008CFF">${items[0]}</span>`);
  };

  // 添加话题
  const handleAddTopic = () => {
    run2({ name: items[0] });
  };

  // 初始化数据
  useEffect(() => {
    setList([]);
    setPageNo(1);
    setTotal(0);
    setLoaded(false);
    setSelectedIndex(0);
    run({ keyword: items[0], pageNo: 1, pageSize: 20 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [items]);

  // 监听列表滚动加载更多
  useEffect(() => {
    const { current } = listWrapRef;

    const handleScroll = debounce(() => {
      if (current) {
        // 获取元素的总高度
        const elementHeight = current.scrollHeight;
        // 获取元素视口高度
        const viewportHeight = current.clientHeight;
        // 获取当前滚动位置
        const scrollPosition = current.scrollTop;

        // 检查是否滚动到底部
        if (scrollPosition + viewportHeight >= elementHeight - 20) {
          if (loading) {
            return;
          }
          if (total === list.length) {
            return;
          }

          // 加载更多数据
          const no = pageNo + 1;
          setPageNo(no);
          run({ keyword: items[0], pageNo: no, pageSize: 20 });
        }
      }
    }, 500);

    // 添加 scroll 事件监听器
    current?.addEventListener('scroll', handleScroll);

    return () => {
      current?.removeEventListener('scroll', handleScroll);
    };
  }, [listWrapRef, list, items, loading, pageNo, run, total]);

  useImperativeHandle(ref, () => ({
    onKeyDown: (event) => {
      if (event.key === 'ArrowUp') {
        setSelectedIndex((selectedIndex + list.length - 1) % list.length);
        return true;
      }

      if (event.key === 'ArrowDown') {
        setSelectedIndex((selectedIndex + 1) % list.length);
        return true;
      }

      if (event.key === 'Enter') {
        selectItem(selectedIndex);
        return true;
      }

      return false;
    },
  }));

  return (
    <div ref={listWrapRef} className="topic-list-wrap">
      {/* 数据列表 */}
      {list.map((item, index) => (
        <div
          key={item.id}
          onClick={() => selectItem(index)}
          dangerouslySetInnerHTML={{
            __html: textHighLight(item.name),
          }}
          className={classNames('topic-item', index === selectedIndex ? 'is-selected' : '')}
        />
      ))}

      {/* 结果状态 */}
      <div className="list-result">
        {!loaded ? <Spin /> : null}
        {loaded && !total ? (
          <div className="add-topic" onClick={handleAddTopic}>
            <span>#{items[0]}</span>
            <span className="add-btn">
              <Icon name="add_line" />
              添加新话题
            </span>
          </div>
        ) : null}
        {loaded && list.length === total && total > 20 ? <div>加载完</div> : null}
        {loaded && list.length !== total ? <div>{loading ? <Spin /> : '加载更多数据'}</div> : null}
      </div>
    </div>
  );
});

export default MentionList;
