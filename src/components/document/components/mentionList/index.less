@import '../../../../styles/mixins.less';

.topic-list-wrap {
  width: 400px;
  max-height: 240px;
  overflow-y: auto;
  background: #fff;
  border: 1px solid rgba(61, 37, 20, 0.05);
  border-radius: 0.7rem;
  box-shadow: 0px 12px 33px 0px rgba(0, 0, 0, 0.06), 0px 3.618px 9.949px 0px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  gap: 0.1rem;
  overflow: auto;
  padding: 0.4rem;
  position: relative;

  .topic-item {
    cursor: pointer;
    border: none;
    padding: 0 8px;
    border-radius: 10px;
    line-height: 32px;
    // 多行文本溢出
    // .ellipsis-multiple();

    &:hover,
    &:hover.is-selected {
      background: rgba(217, 238, 255, 0.3);
    }

    &.is-selected {
      background: rgba(217, 238, 255, 0.3);
    }
  }

  .list-result {
    > div {
      height: 40px;
      .flex-layout(@alignItems: center, @justifyContent: center);
    }

    .add-topic {
      padding: 0 8px;
      .flex-layout(@alignItems: center; @justifyContent: space-between);

      .add-btn {
        cursor: pointer;
        color: #008cff;
      }
    }
  }
}

.tiptap-extension-topic-item {
  color: #008cff;
}
