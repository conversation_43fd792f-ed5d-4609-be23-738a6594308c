.doc-wrap {
  --el-max-w: 674px;
  --m-l-r: auto;
  --table-w: 100%;

  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  position: relative;
  // padding: 16px;

  &.middle {
    --m-l-r: 24px;
    --table-w: calc(100% - 24px);
  }

  &.small {
    --m-l-r: 12px;
    --table-w: calc(100% - 24px);
  }

  ul,
  ol {
    padding: 0 0 0 2em;
  }

  ul li,
  ol li {
    margin-bottom: 4px;
    list-style-type: auto;
    list-style-position: outside;
  }

  .doc-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
  }

  .editor {
    :global {
      .mil-editor-provider-container {
        > * {
          max-width: var(--el-max-w) !important;
          margin-left: var(--m-l-r) !important;
          margin-right: var(--m-l-r) !important;
        }

        > .react-renderer {
          margin: 0 !important;
          max-width: none !important;

          > * {
            max-width: var(--el-max-w);
            margin-left: var(--m-l-r) !important;
            margin-right: var(--m-l-r) !important;
          }
        }

        > table {
          width: var(--table-w) !important;
        }

        :is(h1, h2, h3, h4, h5, h6) p {
          line-height: 1.5;
        }

        :is(h1, h2, h3, h4, h5, h6) {
          & + p {
            margin-top: 16px;
          }
        }

        :is(h1, h2, h3, h4, h5, h6) + :is(h1, h2, h3, h4, h5, h6) {
          margin-top: 20px;
        }

        p {
          & + :is(h1, h2, h3, h4, h5, h6) {
            margin-top: 36px;
          }
        }

        p + :is(p) {
          margin-top: 8px;
        }

        p + :is(.table-wrapper) {
          margin-top: 16px;
        }

        .table-wrapper + :is(.table-wrapper, p) {
          margin-top: 16px;
        }

        .table-wrapper + :is(h1, h2, h3, h4, h5, h6) {
          margin-top: 36px;
        }

        & > .react-renderer {
          & + .react-renderer:not(.node-image):not(.node-hotZone):not(.node-swiper) {
            margin-top: 16px !important;
          }
        }

        :is(h1, h2, h3, h4, h5, h6, p) {
          & + .react-renderer:not(.node-image):not(.node-hotZone):not(.node-swiper) {
            margin-top: 16px !important;
          }
        }

        .react-renderer {
          & + p {
            margin-top: 16px;
          }
        }
      }

      /* Give a remote user a caret */
      .collaboration-cursor__caret {
        border-left: 1px solid #0d0d0d;
        border-right: 1px solid #0d0d0d;
        margin-left: -1px;
        margin-right: -1px;
        pointer-events: none;
        position: relative;
        word-break: normal;
      }

      /* Render the username above the caret */
      .collaboration-cursor__label {
        border-radius: 3px 3px 3px 0;
        color: #fff;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        left: -1px;
        line-height: normal;
        padding: 2px 4px;
        position: absolute;
        top: -1.6em;
        user-select: none;
        white-space: nowrap;
        z-index: 2;
      }
    }
  }

  :global {
    .contentBox {
      max-width: var(--el-max-w);
      margin: 0 var(--m-l-r);
    }
  }
}
