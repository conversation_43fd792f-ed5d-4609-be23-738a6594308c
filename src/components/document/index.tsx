import * as Y from 'yjs';
import { useSize } from 'ahooks';
import Editor, {
  useEditorState,
  setPageData,
  closeSendCommands,
  BubbleFormatQuickBar,
} from '@echronos/editor/dist/core';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import { message } from '@echronos/antd';
import { IndexeddbPersistence } from 'y-indexeddb';
import { useNavigate } from 'react-router-dom';
import Collaboration from '@tiptap/extension-collaboration';
import type { DocumentHeadCoverData } from '@echronos/millet-ui';
import { Editor as TiptapEditor, JSONContent, Content } from '@tiptap/core';
import { PanelHandle } from '@echronos/editor/dist/node-panel-group';
import WebsocketProvider from '@echronos/editor/dist/pkg-y-websocket';
import { useEffect, useState, CSSProperties } from 'react';
import CollaborationCursor from '@echronos/editor/dist/extension-collaboration-cursor';
import { useUserInfoStore } from '@/store';
import useDocStore, { User } from '@/store/useDocStore';
import { getPageBlocks, getPagePer } from '@/apis';
import transformProtocols from '@/utils/transformProtocols';
import getIframeToParentChannel from '@/service/iframe-to-parent-channel';
import extensions from './extensions';
import SelectGoods from './components/select-goods';
import DocBreadcrumb from './components/breadcrumb';
import Head, { toObj } from './components/header';
import Publish from './components/publish';
import styles from './index.module.less';

/** 文本类节点 */
const textLike = ['heading1', 'heading2', 'heading3', 'paragraph'];

/** 用户颜色 */
const userNameColorArr = ['#6600FF', '#004DFF', '#DE00C0', '#E18C3E', '#7100D3'];

/** 默认文档头部数据 */
const defaultData = {
  title: '',
  avatar: {
    url: '',
    type: 'icon',
  },
  background: {
    url: '',
    transform: 29,
  },
};

// 全局 editor
let outEditorIns: null | TiptapEditor = null;

// 在线用户
let onlineUsersGlobal = [] as User[];

interface DocProps {
  /** 文档 id */
  blockId: string;
}

function Document({ blockId }: DocProps) {
  const navigate = useNavigate();

  const [extensionsConfig, setExtensionsConfig] = useState<any[]>(extensions);
  const [handlesConfig] = useState([PanelHandle]);
  const [ydocIns, setYdocIns] = useState<Y.Doc | null>(null);
  const [visible, setVisible] = useState(false);
  const [watchUpdate, setWatchUpdate] = useState(false);
  const [editorStyle, setEditorStyle] = useState<CSSProperties>({});
  const [goodsDranwer, setGoodsDranwer] = useState(false);
  const [selfType, setSelfType] = useState(false); // true自营，false分销
  // 文档加载完成, 可以执行操作
  const [docLoaded, setDocLoaded] = useState(false);
  // 后续插入数据
  const [insertData, setInsertData] = useState<Content>([]);
  // 文档导航栏是否可见
  const [isShowNav, setIsShowNav] = useState(true);

  const { user } = useUserInfoStore();

  // @ts-expect-error todo
  const { userId, nickname } = user.user;

  const {
    isShowPublish,
    updateIsShowPublish,
    setOnlineUsers,
    setDocLoading,
    socketProvider,
    setSocketProvider,
    setIndexDBProvider,
    updateDocHeadInfo,
    updateDocBlockId,
    updateDocSpaceId,
  } = useDocStore();
  const { editable, setEditable, editor, content, setEditor, setContent } = useEditorState();

  // 文档容器尺寸
  const size = useSize(document.querySelector('#editorProviderPartent'));

  // 编辑器初始化回调
  const onEditorCreate = (e: TiptapEditor) => {
    setEditor(e);
  };
  const update = (e: DocumentHeadCoverData) => {
    try {
      const data = toObj(e);
      updateDocHeadInfo(data);
      console.log('[log 222]: ', 222);
      console.log('[log socketProvider]: ', socketProvider);
      socketProvider?.ws?.send(
        JSON.stringify({
          type: 'EDIT',
          content: { head: data, blockId },
          message: '',
        })
      );
    } catch (error) {
      console.log('错误-----fffffff', error);
    }
  };

  /** 文档插入后续数据 */
  const handleInertDoc = () => {
    const { editor: _editor } = useEditorState.getState();

    if (_editor) {
      // 插入到文档底部
      const { state } = _editor;
      const { lastChild } = state.doc;
      setTimeout(() => {
        // 如果最后一个节点是空文本类节点, 插入内容, 否则创建新的一行插入内容
        if (lastChild?.childCount === 0 && textLike.includes(lastChild.type.name)) {
          _editor.commands.insertContentAt(state.doc.content.size - 1, insertData);
        } else {
          _editor.commands.insertContentAt(state.doc.content.size, insertData);
        }
        _editor.commands.focus('end');
      }, 500);
    }
  };

  // 初始化文档数据
  useEffect(() => {
    setVisible(false);
    setDocLoading(true);
    setWatchUpdate(false);
    setEditor(null);
    updateIsShowPublish(false);

    if (blockId) {
      getPageBlocks({ blockId })
        .then((res) => {
          // 处理权限
          getPagePer({ blockId }).then((res2: any) => {
            const { perCodes } = res2;
            // 页面存在
            if (res2.isExist) {
              if (perCodes.length) {
                // 查看/编辑
                if (perCodes.includes('BS_001_001') || perCodes.includes('BS_001_002')) {
                  setEditable(true);
                } else if (perCodes.includes('BS_001_003')) {
                  // 仅查看
                  setEditable(false);
                }
              }
            }
          });
          if (res.head.avatar !== undefined) {
            const data: DocumentHeadCoverData = {
              title: res.head.title,
              background: {
                transform: res.head.background.transform,
                url: res.head.background.url,
              },
              avatar: {
                type: res.head.avatar.type,
                url: res.head.avatar.url || '',
              },
            };
            updateDocHeadInfo(data);
          } else {
            const data: DocumentHeadCoverData = {
              title: res.attrs.pageName || '',
              background: {
                transform: defaultData.background.transform,
                url: defaultData.background.url,
              },
              avatar: {
                type: defaultData.avatar.type,
                url: res.attrs.logo || defaultData.background.url,
              },
            };
            updateDocHeadInfo(data);
          }
          // 处理文档数据格式
          const jsonContent = transformProtocols(res, {}) as JSONContent;
          console.log('json---', jsonContent);
          setContent(jsonContent);

          // 储存文档数据
          Object.keys(res.block).forEach((item) => {
            const data = res.block[item];
            // @ts-expect-error todo
            res.block[item] = {
              type: data.type,
              attrs: data.attrs,
              blockId: data.blockId,
              content: data.content,
              parentId: data.parentId,
            };
          });
          setPageData({
            data: res,
            spaceId: jsonContent.spaceId,
            pageId: (jsonContent.attrs && jsonContent.attrs.blockId) || '',
            publishStatus: res.publishStatus || 0,
          });

          // 设置文档是否触发更新状态
          setWatchUpdate(true);

          // 更新文档 id
          updateDocBlockId(blockId);

          // 更新文档 spaceId
          updateDocSpaceId(jsonContent.spaceId);

          // 显示导航栏
          setIsShowNav(true);

          // 处理 socket 逻辑
          // 销毁存在的链接
          if (socketProvider) {
            socketProvider.isInitiativeCloseStatus = true;
            socketProvider.destroy();
            setSocketProvider(null);
          }
          closeSendCommands();
          setYdocIns(null);

          // 异步创建新链接
          setTimeout(() => {
            try {
              const ydoc = new Y.Doc();
              // 删除已经存在的缓存数据
              indexedDB.deleteDatabase(`doc-${blockId}`);
              // 初始化 indexDB 离线储存协同消息
              setIndexDBProvider(new IndexeddbPersistence(`doc-${blockId}`, ydoc));

              // 创建新的链接
              const provider = new WebsocketProvider(
                // @ts-expect-error todo
                import.meta.env.BIZ_DOC_SOCKET_API_URL,
                // 'ws://***************:8185',
                // 'ws://localhost:3001'
                `ws`,
                ydoc,
                {
                  // connect: false,
                  params: {
                    bizType: 'doc', // 业务类型
                    bizId: blockId, // 文档 id
                  },
                  headers: {
                    // 用户 token
                    Authorization: localStorage
                      .getItem('CURRENT_SESSION_TOKEN')
                      ?.split('bearer ')[1],
                  },
                  callbacks: {
                    // 处理在线用户
                    handleOnlineUsers(data: User[]) {
                      setOnlineUsers(data);
                      const newIds = data.map((item) => item.id);
                      const oldIds = onlineUsersGlobal.map((item) => item.id);
                      const delIds = oldIds.filter((item) => !newIds.includes(item));
                      // 删除不存在的节点
                      delIds.forEach((item) => {
                        (document.querySelector(`.cursor-${item}`) as Element)?.remove();
                      });
                      onlineUsersGlobal = data;
                    },
                    // 处理头部组件
                    handleHead(data: DocumentHeadCoverData) {
                      updateDocHeadInfo(data);
                    },
                    // socket 连接成功
                    handleSocketOnopen() {},
                    /**
                     * socket 断开连接, 判断是否是因为没有权限或者页面被删除
                     * 导致的断开,如果是则要跳到缺省页面, 否则按照正常断开连接处理
                     */
                    handleSocketonClose(pro: any) {
                      return new Promise((resolve, reject) => {
                        // 如果是自己主动关闭连接, 不需要校验权限和页面
                        if (pro.isInitiativeClose) {
                          resolve(undefined);
                        } else {
                          // 处理权限
                          getPagePer({ blockId }).then((res2: any) => {
                            const { perCodes } = res2;
                            if (res2.isExist) {
                              if (perCodes.length) {
                                if (perCodes.includes('BS_001_003')) {
                                  // 仅查看
                                  setEditable(false);
                                  // 主动关闭发送连接, 防止出现关闭连接提示
                                  if (pro) {
                                    // eslint-disable-next-line no-param-reassign
                                    pro.isInitiativeCloseStatus = true;
                                  }
                                  message.info('页面权限变更为仅查看, 最近编辑的内容不会被保存');
                                  outEditorIns?.commands.blur();
                                }
                                resolve(undefined);
                              } else {
                                // 页面被删除
                                message.info('页面已经被删除或者权限变更');
                                navigate('/data/no-perm?type=page');
                                reject(new Error('页面已经被删除或者权限变更'));
                              }
                            } else {
                              // 页面被删除
                              message.info('页面已经被删除或者权限变更');
                              navigate('/data/no-perm?type=page');
                              reject(new Error('页面已经被删除或者权限变更'));
                            }
                          });
                        }
                      });
                    },
                    // socket 报错
                    handleSocketOnerror() {},
                    // socket 接收消息
                    handleSocketOnmessage() {},
                  },
                  // @ts-expect-error todo
                  docType: 4,
                }
              );

              // 初始化扩展
              setExtensionsConfig([
                ...extensions,
                Collaboration.configure({ document: ydoc }),
                CollaborationCursor.configure({
                  provider,
                  user: {
                    id: userId,
                    name: nickname,
                    color: userNameColorArr[Math.floor(Math.random() * userNameColorArr.length)],
                  },
                  render: (params: any) => {
                    const showName = onlineUsersGlobal.find(
                      (item) => item.id === params.id
                    ) as User;
                    // 指针
                    const cursor = document.createElement('span');
                    cursor.classList.add('collaboration-cursor__caret', `cursor-${params.id}`);
                    cursor.setAttribute('style', `border-color: ${params.color}`);

                    // 昵称
                    const label = document.createElement('div');
                    label.classList.add('collaboration-cursor__label', `user-${params.id}`);
                    label.setAttribute('style', `background-color: ${params.color}`);
                    label.insertBefore(document.createTextNode(showName?.nickname), null);
                    cursor.insertBefore(label, null);
                    console.log('onlineUsersGlobal', onlineUsersGlobal);
                    console.log('cursor=render====', showName, userId, params.id);
                    if (!showName || userId === params.id) {
                      return document.createElement('span');
                    }
                    return cursor;
                  },
                }),
              ]);
              setYdocIns(ydoc);
              setSocketProvider(provider);

              /**
               * todo: ydoc 数据合并问题
               * 1.第一次 setTimeout:
               * 初始化文档数据时, 文档会基于获取到的数据生成本地 ydoc 数据, 随后
               * 发送 ydoc 数据到其他客户端进行数据合并, 此时无法正确合并, 会产生
               * 多余数据, 因此需要懒加载本地文档的初始化过程, 先获取其他端同步过
               * 来的数据, 再与本地数据进行合并
               * 2.第一次 setTimeout:
               * 等文档初始化并完成数据合并后, 再允许用户编辑, 否则会产生数据异常
               */
              setTimeout(() => {
                setVisible(true);
                setTimeout(() => {
                  setDocLoading(false);
                  setDocLoaded(true);
                }, 1000);
              }, 2000);
            } catch (error) {
              setDocLoading(false);
              console.log('[log socket init error]: ', error);
            }
          });
        })
        .catch((error) => {
          setDocLoading(false);
          console.log('[get doc data error api]:', error);
          if ([5, 10004].includes(error.code)) {
            navigate('/data/no-perm?type=page');
          } else {
            message.error(error.message);
          }
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [blockId]);
  // 注册添加悬浮导航
  useEffect(() => {
    if (editor) {
      outEditorIns = editor;
    }
  }, [editor]);

  // 处理文档是否可编辑
  useEffect(() => {
    setEditorStyle({ ...editorStyle, pointerEvents: editable ? 'unset' : 'none' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editable]);

  // 离开文档页面断开 socket
  useEffect(() => {
    return () => {
      if (socketProvider) {
        socketProvider.isInitiativeCloseStatus = true;
        socketProvider.destroy();
        setSocketProvider(null);
      }
      closeSendCommands();
    };
  }, [socketProvider, setSocketProvider]);

  // 监听从搜索页面传递的数据
  useEffect(() => {
    getIframeToParentChannel().invoke('getDocData', {
      success: (e) => {
        if (e?.data?.docData) {
          setInsertData(e.data.docData);
        }
      },
    });
  }, []);

  // 监听从搜索页面传递的数据, 用于插入到文档中
  useEffect(() => {
    if (insertData?.length && docLoaded && editor) {
      handleInertDoc();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [insertData, docLoaded, editor]);

  return (
    <div
      className={classNames(
        styles['doc-wrap'],
        'workspace-main',
        size && size?.width < 1000 && styles.middle,
        size && size?.width < 600 && styles.small
      )}
    >
      <Helmet>
        <meta name="referrer" content="no-referrer" />
      </Helmet>

      {/* 导航栏 */}
      {isShowNav && <DocBreadcrumb />}

      {/* 文档体 */}
      <div className={styles['doc-content']}>
        {/* 文档头 */}
        {socketProvider && <Head onUpdate={update} />}

        {/* 文档内容 */}
        {content && ydocIns && socketProvider && visible && (
          <Editor
            editorProviderClassName={styles.editor}
            content={content}
            style={editorStyle}
            onCreate={onEditorCreate}
            watchUpdate={watchUpdate}
            handlesConfig={handlesConfig}
            extensionsConfig={extensionsConfig}
          >
            {/* 快捷菜单 */}
            <BubbleFormatQuickBar
              config={[
                'TransformTextTag',
                'TransformFontSize',
                'Space',
                'TransformTextColor',
                'TransformSpacing',
                'TransformTextAlign',
                'More',
              ]}
            />
          </Editor>
        )}

        {/* 发布笔记 */}
        {isShowPublish && (
          <Publish
            blockId={blockId}
            editor={editor}
            setSelfType={setSelfType}
            setGoodsDranwer={setGoodsDranwer}
          />
        )}
      </div>

      {/* 选择商品 */}
      {goodsDranwer && (
        <SelectGoods
          editor={editor}
          goodsDranwer={goodsDranwer}
          selfType={selfType}
          onClose={() => {
            setGoodsDranwer(false);
          }}
        />
      )}
    </div>
  );
}

export default Document;
