import { Color } from '@tiptap/extension-color';
import Focus from '@tiptap/extension-focus';
import Highlight from '@tiptap/extension-highlight';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';

import { PluginKey } from '@tiptap/pm/state';
import FullColumnExtension from '@echronos/editor/dist/extension-full-column';
import FontSizeExtension from '@echronos/editor/dist/extension-font-size';
import SpacingExtension from '@echronos/editor/dist/extension-spacing';
import LineHeightExtension from '@echronos/editor/dist/extension-line-height';
import { selectableNodeCls } from '@echronos/editor/dist/core';
import PasteExtension from '@echronos/editor/dist/extension-paste';
import ClipboardSerializerExtension from '@echronos/editor/dist/extension-clipboard-serializer';
import InputExtension from '@echronos/editor/dist/extension-input';
import BehaviourModifyExtension from '@echronos/editor/dist/extension-behaviour-modify';
import CustomButtonNode from '@echronos/editor/dist/node-custom-button';
import SlashMenuExtension from '@echronos/editor/dist/extension-bubble-slash-menu-doc';
import CodeBlockLowlight from '@echronos/editor/dist/extension-code-block-lowlight';
import ColumnBlockSchema from '@echronos/editor/dist/extension-column';
import PanelGroupNode from '@echronos/editor/dist/node-panel-group';
import PanelNode from '@echronos/editor/dist/node-panel';
import DivderNode from '@echronos/editor/dist/node-divider';
import ImageNode from '@echronos/editor/dist/node-image';
import TableNode from '@echronos/editor/dist/node-table';
import TableHeaderNode from '@echronos/editor/dist/node-table-header';
import TableCellNode from '@echronos/editor/dist/node-table-cell';
import TableRowNode from '@echronos/editor/dist/node-table-row';
import SwiperNode from '@echronos/editor/dist/node-swiper';
import HotZoneNode from '@echronos/editor/dist/node-hot-zone';
import LinkBlockSchema from '@echronos/editor/dist/extension-link';
import CreateBlockIdExtension from '@echronos/editor/dist/extension-create-block-id';
// import EnterHandlerExtension from '@echronos/editor/dist/extension-enter-handler';
import ParagraphNode from '@echronos/editor/dist/node-paragraph';
import AppendParagraphExtension from '@echronos/editor/dist/extension-append-paragraph';
import ContainerNode, {
  HeaderContainerNode,
  FooterContainerNode,
} from '@echronos/editor/dist/node-container';
import HeadingNode from '@echronos/editor/dist/node-heading';
import ExtensionAiChat from '@echronos/editor/dist/extension-ai-chat';
import BulletListNode from '@echronos/editor/dist/node-bullet-list';
import ListItemNode from '@echronos/editor/dist/node-list-item';
import OrderedListNode from '@echronos/editor/dist/node-ordered-list';
import NodeEmoji from '@echronos/editor/dist/node-emoji';
import NodeMention from '@echronos/editor/dist/node-mention';
import CardBoxNode from '@echronos/editor/dist/node-card-box';
import GoodsDistributionCardNode from '@echronos/editor/dist/node-goods-distribution';
import { getSiteMenuBlocks } from '@/apis';
import MentionList from './components/mentionList';

const extendCls = (extensionName: string, isDraggabe = true) =>
  `${isDraggabe ? selectableNodeCls : ''} node-${extensionName}`;

const commonClsConfig = () => ({
  HTMLAttributes: {
    class: selectableNodeCls,
  },
});

const extensionsConfig = [
  CreateBlockIdExtension,
  AppendParagraphExtension,
  InputExtension,
  StarterKit.configure({
    history: false,
    paragraph: {
      HTMLAttributes: {
        class: extendCls('paragraph'),
      },
    },
    heading: {
      levels: [1, 2, 3, 4, 5, 6],
      HTMLAttributes: {
        class: extendCls('heading'),
      },
    },
    horizontalRule: false,
    dropcursor: {
      color: '#4096ff',
      width: 2,
      class: 'block__dropcursor',
    },
    code: {
      HTMLAttributes: {
        class: 'block__inline-code',
      },
    },
    codeBlock: { ...commonClsConfig() },
    bulletList: false,
    orderedList: false,
    listItem: false,
  }),
  ParagraphNode,
  HeadingNode,
  PanelGroupNode,
  PanelNode,
  BulletListNode,
  ListItemNode,
  OrderedListNode,
  // EnterHandlerExtension,
  FontSizeExtension,
  Color,
  Highlight.configure({ multicolor: true }),
  Underline,
  ContainerNode,
  CustomButtonNode,
  Placeholder.configure({
    placeholder({ node }) {
      if (node.type.name === 'heading') {
        return `标题${node.attrs.level}`;
      }
      return '输入"/" 使用命令';
    },
    emptyEditorClass: 'block__editor-empty',
    emptyNodeClass: 'block__node-empty',
    includeChildren: true,
  }),
  // 当前编辑聚集块选中高亮提示
  Focus.configure({
    className: 'block__focus',
    mode: 'deepest',
  }),
  LinkBlockSchema.configure({
    openOnClick: false,
    validate: (link) => /^https?:\/\//.test(link),
    HTMLAttributes: {
      class: 'block__link',
      'data-link': 'link',
    },
  }),
  CodeBlockLowlight,
  FullColumnExtension.configure({ types: ['paragraph'] }),
  ColumnBlockSchema,
  SlashMenuExtension.configure({
    suggestion: {
      char: '/',
      pluginKey: new PluginKey('slash'),
    },
    // 菜单接口
    getMenuListApi: getSiteMenuBlocks,
  }),
  DivderNode,
  ImageNode.configure({
    businessType: 'doc',
  }),
  SwiperNode.configure({
    businessType: 'doc',
  }),
  HotZoneNode.configure({
    businessType: 'doc',
  }),
  HeaderContainerNode,
  FooterContainerNode,
  BehaviourModifyExtension,
  ClipboardSerializerExtension,
  PasteExtension,
  TextAlign.configure({
    types: ['heading', 'paragraph'],
    alignments: ['left', 'center', 'right'],
  }),
  SpacingExtension.configure({
    types: ['heading', 'paragraph'],
  }),
  LineHeightExtension.configure({
    types: ['heading', 'paragraph'],
  }),
  ExtensionAiChat.configure({
    popcontainer: '#editorProviderPartent',
    popTop: 300,
    popWidth: 600,
  }),
  TableNode,
  TableHeaderNode,
  TableRowNode,
  TableCellNode,
  NodeEmoji,
  NodeMention.configure({
    HTMLAttributes: {
      class: 'tiptap-extension-topic-item',
    },
    suggestion: {
      char: '#',
      pluginKey: new PluginKey('topic'),
      items: ({ query }) => {
        return [query];
      },
    },
    menuList: MentionList,
  }),
  CardBoxNode,
  GoodsDistributionCardNode,
];

export default extensionsConfig;
