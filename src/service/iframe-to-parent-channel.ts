import { createIframeToParentChannel, InvokeParams } from '@echronos/core';

let channel = null as unknown as {
  // eslint-disable-next-line no-unused-vars
  invoke: (eventName: string, params?: InvokeParams) => void;
  // eslint-disable-next-line no-unused-vars
  emit: (eventName: string, params?: unknown) => void;
  destroy: () => void;
};

// eslint-disable-next-line no-unused-vars
const channelEvent: Record<string, (res?: unknown) => void> = {};

// eslint-disable-next-line no-unused-vars
export const setChannelEvent = (key: string, cb: (res?: any) => void) => {
  channelEvent[key] = cb;
};

export const setChannelEvents = (params: Record<string, (res?: any) => void>) => {
  Object.keys(params).forEach((key: string) => {
    channelEvent[key] = params[key];
  });
};

const getIframeToParentChannel = () => {
  if (!channel) {
    // @ts-ignore
    channel = createIframeToParentChannel({
      targetOrigin: import.meta.env.BIZ_ECHOS_DOMAIN || 'http://localhost:9000',
      events: {
        panelStatus: (res: boolean) => {
          if (channelEvent.panelStatus) {
            channelEvent.panelStatus(res);
          }
        },
        locationChange: (res: any) => {
          if (channelEvent.locationChange) {
            channelEvent.locationChange(res);
          }
        },
        newPanelSession: (res: any) => {
          if (channelEvent.newPanelSession) {
            channelEvent.newPanelSession(res);
          }
        },
        quitPanelSession: () => {
          if (channelEvent.newPanelSession) {
            channelEvent.quitPanelSession();
          }
        },
        appendPanelTreeNode: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.appendPanelTreeNode(res);
          }
        },
        interPanelTreeNode: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.interPanelTreeNode(res);
          }
        },
        appendPanelOrderTreeNodes: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.appendPanelOrderTreeNodes(res);
          }
        },
        appendPanelTreeSummaryNode: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.appendPanelTreeSummaryNode(res);
          }
        },
        appendPanelTreeEndNode: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.appendPanelTreeEndNode(res);
          }
        },
        updatePanelTreeNode: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.updatePanelTreeNode(res);
          }
        },
        closePanelTreeNodeLoading: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.closePanelTreeNodeLoading(res);
          }
        },
        localPanelRootCenter: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.localPanelRootCenter(res);
          }
        },
        clearPanelTreeNodes: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.clearPanelTreeNodes(res);
          }
        },
        deletePanelTreeNodeChildrenById: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.deletePanelTreeNodeChildrenById(res);
          }
        },
        deletePanelNodeById: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.deletePanelNodeById(res);
          }
        },
        togglePanelAgent: (res: any) => {
          if (channelEvent.appendPanelTreeNode) {
            channelEvent.togglePanelAgent(res);
          }
        },
        updateDrawerFull: (res: any) => {
          if (channelEvent.updateDrawerFull) {
            channelEvent.updateDrawerFull(res);
          }
        },
      },
    });
  }
  return channel;
};

export default getIframeToParentChannel;
