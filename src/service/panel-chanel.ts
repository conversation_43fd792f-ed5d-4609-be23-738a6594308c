/* eslint-disable no-unused-vars */
import {
  IAppendWldTreeNode,
  IUpdateWildTreeNode,
  IInsterWldTreeNode,
  INodeData,
  IASKData,
  IContentData,
} from '@/interface/ai-channel-types';

interface IEvent {
  appendPanelTreeNode: (data: IAppendWldTreeNode<INodeData | null>) => void;
  interPanelTreeNode: (data: IInsterWldTreeNode<INodeData>) => void;
  appendPanelOrderTreeNodes: (data: IAppendWldTreeNode<INodeData>[]) => void;
  appendPanelTreeSummaryNode: (data: IAppendWldTreeNode<IASKData | IContentData>) => void;
  appendPanelTreeEndNode: (data: IAppendWldTreeNode<null>) => void;
  updatePanelTreeNode: (data: IUpdateWildTreeNode<INodeData>) => void;
  closePanelTreeNodeLoading: (data: { id: string; sessionId: string }) => void;
  closePanelTreeAllNodeLoading: (data: { sessionId: string }) => void;
  localPanelRootCenter: () => void;
  clearPanelTreeNodes: () => void;
  deletePanelTreeNodeChildrenById: (data: { id: string; sessionId: string }) => void;
  deletePanelNodeById: (data: { id: string; sessionId: string }) => void;
  togglePanelAgent: (agentType: '0' | '1' | '2') => void;
  newPanelSession: (id: string) => void;
  quitPanelSession: () => void;
  openPanelInDrawer: () => void;
  openLink: (data: { url: string; title?: string }) => void;
}

let events = {} as unknown as IEvent;

export const createPanelChanel = (e: Record<string, (params?: any) => void>) => {
  events = {
    ...events,
    ...e,
  };
};

let chanel = null as unknown as IEvent;

export const getPanelChanel = () => {
  if (!chanel && window.microApp?.getData()) {
    chanel = window.microApp?.getData() as unknown as IEvent;
  }
  return chanel;
};
