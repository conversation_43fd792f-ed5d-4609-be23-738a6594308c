import { message } from 'antd';
import ModalConfirm from '@/components/modal';
import { permMemberRemoveApi, permSpaceDeleteApi, permMemberAddListApi } from '@/apis';
import { user as useUserStore, space as useSpaceStore } from '@/store';
import { removeTree } from '@/utils/handle';
import { cloneDeep } from 'lodash';
import { NavigateFunction } from 'react-router-dom';
import selectUsers from '@/components/select-users';
import type { Item } from '@/components/select-users/util';
import { BodyInstance } from '@/components/select-users/body';

/**
 *  @description 删除空间
 */
export function onDeleteSpace(spaceId: string, navigate: NavigateFunction) {
  const space = useSpaceStore.getState();
  // 获取当前页面信息
  const pageInfo = cloneDeep(space.pageInfo);
  const spaceList = cloneDeep(space.spaceGroupList);
  ModalConfirm({
    title: '提示',
    icon: '',
    centered: true,
    content: '删除空间后该空间将无法继续使用，空间内所有页面将被删除，确定删除此空间？',
    onOk: () => {
      permSpaceDeleteApi({ spaceId }).then(() => {
        message.success('删除成功');
        const arr = space.spaceGroupList;
        /** 移除树节点 */
        removeTree(arr, spaceId);
        space.setSpaceGroupList(cloneDeep(arr));
        if (pageInfo.spaceId === spaceId) {
          console.log('123');
          navigate('/pages/?isRefreshAll=1', { replace: true });
        } else {
          const narr = spaceList[0].children.filter((item) => item.spaceId !== spaceId);
          const arr2 = spaceList[1].children.filter((item) => item.spaceId !== spaceId);
          const groups = [
            {
              id: '001',
              name: '我的空间',
              key: '001',
              children: narr,
              spaceId: '',
              logo: '',
              description: '',
              roleIds: [],
              type: '',
              blockId: '',
              spaceName: '',
              spaceLogo: '',
              pageList: [],
              content: [],
              ownerId: 0,
              hasChild: narr.length > 0,
              parentId: '',
              perCodes: [],
              isOwner: true,
              attrs: {
                pageName: '',
                logo: '',
              },
              spaceType: 2,
            },
            {
              id: '002',
              name: '共享给我的空间',
              key: '002',
              children: arr2,
              spaceId: '',
              logo: '',
              description: '',
              roleIds: [],
              type: '',
              blockId: '',
              spaceName: '',
              spaceLogo: '',
              pageList: [],
              content: [],
              ownerId: 0,
              hasChild: arr2.length > 0,
              parentId: '',
              perCodes: [],
              isOwner: false,
              attrs: {
                pageName: '',
                logo: '',
              },
              spaceType: 2,
            },
          ];
          space.setSpaceGroupList(cloneDeep(groups));
        }
        // onNavPage({ groupList: arr, shareList: space.spaceShareList, navigate });
      });
    },
  });
}

/**
 * @description 退出空间
 * @param memberRoleId
 * @param spaceId
 */
export function onExitSpace(memberRoleId: number, spaceId: string) {
  ModalConfirm({
    title: '提示',
    icon: '',
    centered: true,
    content: '确认退出该空间？退出后将不可访问空间内的任何页面',
    onOk: () => {
      permMemberRemoveApi({
        memberRoleId,
        spaceId,
      }).then(() => {
        message.success('退出成功');
      });
    },
  });
}

interface AddMemberProps {
  spaceId: string;
  roleId?: string;
  name?: string;
  defaultData?: number[];
  // eslint-disable-next-line no-unused-vars
  onConfirm: (arr: number[]) => void;
  // eslint-disable-next-line no-unused-vars
  onCancel?: () => void;
}

/**
 *  @description 添加成员
 */
export async function onAddMember({
  spaceId,
  roleId = '',
  name = '',
  defaultData = [],
  onConfirm,
  onCancel,
}: AddMemberProps) {
  const { user } = useUserStore.getState();

  const space = useSpaceStore.getState();
  const res = await permMemberAddListApi({ spaceId, roleId });
  const defaults =
    res.list && res.list.length
      ? res.list.map((item) => ({
          type: item.memberType === 2 ? 'departments' : 'users',
          memberId: item.memberId,
          id: item.memberId,
          label: item.name,
          closable: false,
        }))
      : [];

  selectUsers({
    title: `添加成员至"${name || space.spaceInfo.name}"的空间`,
    companyId: user.user.companyId,
    onlyCompany: true,
    defaults: [...defaults, ...defaultData] as unknown as Item[],
    disables: [
      ...defaults,
      { memberId: user.user.memberId, type: 'users', id: user.user.memberId },
      ...defaultData,
    ] as unknown as Item[],
    // closable: false,
    selectMemberType: 'memberId',
    isDisabledConfirm: true,
    searchIsFilter: null,
    onConfirm: (instance: BodyInstance, close: () => void) => {
      const ids = res.list.map((item) => item.memberId);
      const data = instance.getList();
      const arr = data.filter((item) => !ids.includes(item.id as unknown as number));
      onConfirm(arr as unknown[] as number[]);
      close();
    },
    onCancel,
  });
}
