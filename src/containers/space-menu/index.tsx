import { ReactNode, useState, MouseEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { Popover, Divider, Tooltip } from '@echronos/antd';
import classNames from 'classnames';
import { space as SpaceSotore } from '@/store';
import Icon from '@echronos/echos-icon';
import setPerm from '@/containers/set-perm';
import { PermSpaceListData } from '@/apis';
import { SpaceInfoData } from '@/store/space';
import { onAddMember, onDeleteSpace, onExitSpace } from './space-handle';
import styles from './index.module.less';

interface SpaceMenuProps {
  spaceId: string;
  blockId: string;
  memberRoleId: string;
  onCloseHandle: () => void;
  spaceInfo: PermSpaceListData;
  children: ReactNode;
}

interface MemberData {
  id: number;
  label: string;
  type: string;
  memberId: number;
}

const menu = [
  {
    key: 'add',
    label: '添加成员',
    icon: 'add_line',
    code: ['BS_001_001'],
  },
  {
    key: 'setup',
    label: '空间设置',
    icon: 'set_line',
    code: ['BS_001_001'],
  },
  // {
  //   key: 'out',
  //   label: '退出空间',
  //   icon: 'exit_line',
  //   code: '',
  // },
];

function SpaceMenu({ blockId, spaceId, spaceInfo, onCloseHandle, ...props }: SpaceMenuProps) {
  const navigate = useNavigate();
  const [showPopover, setShowPopover] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const space = SpaceSotore();

  const onClickIcon = (e: MouseEvent) => {
    e.stopPropagation();
    setShowPopover(true);
    setShowTooltip(false);
  };

  const onSetPerm = (val: MemberData[]) => {
    setPerm({
      type: 'space',
      title: '邀请空间成员',
      spaceId,
      defaultData: val,
      addType: 1,
      permCode: spaceInfo.perCodes,
      onInVite: (newval: MemberData[]) => {
        onAddMember({
          spaceId,
          roleId: '',
          name: spaceInfo.name,
          defaultData: newval as unknown as number[],
          onConfirm: (value: number[]) => {
            onSetPerm(value as unknown as MemberData[]);
          },
          onCancel: () => {
            onSetPerm(newval);
          },
        });
      },
    });
  };

  const onSpaceAddMember = () => {
    onAddMember({
      spaceId,
      roleId: '',
      name: spaceInfo.name,
      defaultData: [],
      onConfirm: (value: number[]) => {
        onSetPerm(value as unknown as MemberData[]);
      },
    });
  };

  const onApaceSet = (even: MouseEvent, val: string) => {
    even.stopPropagation();
    setShowPopover(false);
    onCloseHandle();
    switch (val) {
      case 'add':
        onSpaceAddMember();
        break;
      case 'setup':
        space.setSpaceInfo({ ...spaceInfo, blockId } as SpaceInfoData);
        localStorage.setItem('SPACE_INFO', JSON.stringify({ ...spaceInfo, blockId }));
        navigate(`/permission/base?spaceId=${spaceId}&isBack=1`);
        break;
      case 'out':
        onExitSpace(1, spaceId);
        break;
      case 'del':
        onDeleteSpace(spaceId, navigate);
        break;
      default:
        break;
    }
  };

  const content = (
    <>
      {/* 空间更多操作列表 */}
      {menu.map((item) => {
        return (
          (spaceInfo.perCodes.some((code) => item.code.includes(code)) || !item.code) && (
            <div
              className={styles.item}
              key={item.key}
              onClick={(event) => onApaceSet(event, item.key)}
            >
              <Icon className={styles.itemIcon} name={item.icon} />
              <span>{item.label}</span>
            </div>
          )
        );
      })}
      {/* 共享给我的空间不允许删除 */}
      {spaceInfo.isOwner && (
        <>
          <div className={styles.divider}>
            <Divider />
          </div>
          <div
            className={classNames(styles.item, styles.itemDel)}
            onClick={(event) => onApaceSet(event, 'del')}
          >
            <Icon
              className={classNames(styles.itemIcon, styles.itemIconDel)}
              name="delete_trash_line"
            />
            <span>删除空间</span>
          </div>
        </>
      )}
    </>
  );

  return (
    <Popover
      trigger={showPopover ? 'hover' : 'click'}
      placement="bottomLeft"
      overlayClassName={styles.popover}
      content={content}
      visible={showPopover}
      onVisibleChange={(val) => {
        setShowPopover(val);
        if (!val) {
          onCloseHandle();
        }
      }}
    >
      <Tooltip
        placement="bottom"
        title="更多操作"
        visible={showTooltip}
        overlayStyle={{
          width: 'auto',
          height: 'auto',
        }}
      >
        <div
          onClick={onClickIcon}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          {props.children}
        </div>
      </Tooltip>
    </Popover>
  );
}

export default SpaceMenu;
