import { PropsWithChildren, useEffect, useState } from 'react';
import { ModalProps, Input } from '@echronos/antd';
import { SelectEmojiPicture } from '@echronos/millet-ui';
import styles from './index.module.less';

interface SpaceFormData {
  logo: string;
  name: string;
  description: string;
}

export interface SpaceCreateProps extends ModalProps {
  defaultValue?: SpaceFormData;
  // eslint-disable-next-line no-unused-vars
  onChange: (params: SpaceFormData) => void;
  // eslint-disable-next-line no-unused-vars
  onClickEmoji?: (params: boolean) => void;
}

const defaultIcon = 'https://img.huahuabiz.com/user_files/202479/1720496840222857.png';

function SpaceBox({ defaultValue, onChange, onClickEmoji }: PropsWithChildren<SpaceCreateProps>) {
  const [formValue, setFormValue] = useState({
    logo: defaultIcon,
    name: '',
    description: '',
  });

  const onChangeForm = (val: string, key: string) => {
    const form = {
      ...formValue,
      [key]: val,
    };
    setFormValue({ ...form });
    onChange({ ...form });
    onClickEmoji?.(false);
  };

  const onRemoveIcon = () => {
    const info = {
      ...formValue,
      logo: defaultIcon,
    };
    setFormValue({ ...info });
    onChange({
      ...info,
    });
    onClickEmoji?.(false);
  };

  useEffect(() => {
    if (defaultValue) {
      setFormValue({ ...defaultValue });
    }
  }, [defaultValue]);

  return (
    <div className={styles.spaceBox}>
      <div className={styles.label}>创建图标和名称</div>
      <div className={styles.name}>
        <SelectEmojiPicture
          type="emoji"
          onChange={(e) => onChangeForm(e, 'logo')}
          onRemove={onRemoveIcon}
        >
          <div className={styles.icon} onClick={() => onClickEmoji?.(true)}>
            {formValue.logo && <img className={styles.logo} src={formValue.logo} alt="" />}
          </div>
        </SelectEmojiPicture>
        <Input
          value={formValue.name}
          className={styles.nameInput}
          showCount
          maxLength={15}
          placeholder="请输入空间名称"
          onChange={(e) => onChangeForm(e.target.value, 'name')}
        />
      </div>
      <div className={styles.label}>描述</div>
      <Input.TextArea
        value={formValue.description}
        showCount
        maxLength={200}
        placeholder="请输入空间介绍(可选)"
        className={styles.tip}
        onChange={(e) => onChangeForm(e.target.value, 'description')}
      />
    </div>
  );
}

SpaceBox.defaultProps = {
  defaultValue: null,
  onClickEmoji: () => {},
};

export default SpaceBox;
