import { useNavigate, useLocation } from 'react-router-dom';
import classNames from 'classnames';
import styles from './index.module.less';

interface ItemsData {
  label: string;
  key: number;
  url: string;
  baseUrl: string;
}

interface TabsProps {
  items: ItemsData[];
  spaceId: string;
}

export function Tabs({ items, spaceId }: TabsProps) {
  const navigate = useNavigate();
  const location = useLocation();

  const onSelectKey = (url: string) => {
    navigate(`${url}${url.includes('?') ? '&' : '?'}spaceId=${spaceId}`, { replace: true });
  };

  return (
    <div>
      {items.map((item) => {
        return (
          <div
            className={classNames(styles.tabsItem, {
              [styles.tabsItemActive]: location.pathname.includes(item.baseUrl),
            })}
            onClick={() => onSelectKey(item.url)}
            key={item.key}
          >
            {item.label}
          </div>
        );
      })}
    </div>
  );
}

export default Tabs;
