import { PropsWithChildren, useState } from 'react';
import { Modal, ModalProps, message } from '@echronos/antd';
import { isFunction, debounce } from 'lodash';
import { permSpaceCreateApi } from '@/apis';
import { generateUniqueId } from '@/utils/tools';
import SpaceBox from '@/containers/space-box';
import styles from './space-create.module.less';

export interface SpaceCreateProps extends ModalProps {
  onConfirm: () => void;
}

function SpaceCreate({ onConfirm, ...props }: PropsWithChildren<SpaceCreateProps>) {
  const [isDisabled, setIsDisabled] = useState(false);
  const [formValue, setFormValue] = useState({
    logo: '',
    name: '',
    description: '',
  });
  const [showEmoji, setShowEmoji] = useState(false);

  const onSubmit = debounce(() => {
    // const { emoji, picture } = randomEmojiBgImg();
    setIsDisabled(true);
    const blockId = generateUniqueId();
    const plcBlockId = generateUniqueId();
    permSpaceCreateApi({
      ...formValue,
      blockId,
      block: [
        {
          attrs: {
            blockId: plcBlockId,
            class: 'node-selectable',
            content: [],
            fullWidth: 'false',
            lineHeight: 24,
            padding: '0',
            margin: '0',
            parentId: '',
            rootBlockId: '',
            textAlign: 'left',
          },
          blockId: plcBlockId,
          content: [],
          parentId: blockId,
          type: 'paragraph',
        },
      ],
      content: [plcBlockId],
      // head: {
      //   background: {
      //     transform: 50,
      //     url: `https://img.huahuabiz.com/web-document-img/${picture}`,
      //   },
      //   avatar: {
      //     type: 'icon',
      //     url: `https://img.huahuabiz.com/emoji/${emoji}`,
      //   },
      // },
    })
      .then(() => {
        message.success('创建成功');
        onConfirm();
        // @ts-ignore
        props.onClose();
      })
      .finally(() => {
        setIsDisabled(false);
      });
  }, 500);

  const onClose = () => {
    if (!showEmoji) {
      if (isFunction(props.onCancel)) {
        // @ts-ignore
        props.onCancel();
      }
    }
    setShowEmoji(false);
  };

  return (
    <Modal
      {...props}
      centered
      width={440}
      title="创建空间"
      wrapClassName={styles.modal}
      okButtonProps={{ disabled: !formValue.name.length || isDisabled }}
      okText="创建"
      onOk={onSubmit}
      onCancel={onClose}
      confirmLoading={isDisabled}
    >
      <SpaceBox onChange={setFormValue} onClickEmoji={setShowEmoji} />
    </Modal>
  );
}

export default SpaceCreate;
