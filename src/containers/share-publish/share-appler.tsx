// import { useEffect, useState } from 'react';
// import { Modal, ModalProps, Spin } from 'antd';
// import { popupComponent } from '@echronos/react';
// import classNames from 'classnames';
// import { useRequest } from 'ahooks';
// import { getShareAppletCode, ShareAppletCodeData } from '@/apis';
// import styles from './share-applet.module.less';

// interface ShareAppletProps extends ModalProps {
//   blockId: string;
// }

// function ShareApplet({ blockId, ...props }: ShareAppletProps) {
//   const [info, setInfo] = useState<ShareAppletCodeData>({ appletCode: '', blockId: '', id: 0 });

//   const { run, loading } = useRequest(getShareAppletCode, {
//     manual: true,
//     onSuccess: (res) => {
//       setInfo(res);
//     },
//   });

//   useEffect(() => {
//     if (props.visible) {
//       run({ blockId });
//     }
//   }, [run, blockId, props.visible]);

//   return (
//     <Modal {...props} centered width={211} className={styles.modal}>
//       <div className={styles.content}>
//         <div className={styles.title}>扫码分享</div>
//         <Spin spinning={loading} wrapperClassName={styles.spin}>
//           {info.appletCode && <img className={styles.code} src={info.appletCode} alt="" />}
//         </Spin>
//         <div className={classNames(styles.tip, styles.desc)}>微信扫码分享查看</div>
//         <div className={styles.tip}>好友的权限为：可查看</div>
//       </div>
//     </Modal>
//   );
// }

// const shareApplet = (config: ShareAppletProps) => {
//   popupComponent(ShareApplet, { ...config });
// };

// export default shareApplet;
