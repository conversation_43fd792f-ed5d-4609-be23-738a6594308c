import { useEffect, useMemo, useRef, useState } from 'react';
import { Popover, PopoverProps } from '@echronos/antd';
import classNames from 'classnames';
import useUserInfoStore from '@/store/user-info';
import { permMemberCollaboratorListApi } from '@/apis';
import useDocStore from '@/store/useDocStore';
import { useRequest } from 'ahooks';
import { setPerm } from '@/containers';
import getPagePer from '@/apis/site-manager/get-page-per';
import selectUsers from '@/components/select-users';
import type { Item } from '@/components/select-users/util';
import type { ShareInstance } from './share';
import Share from './share';
// import Publish from './publish';
import styles from './index.module.less';

const tabList = [
  {
    label: '分享',
    key: 1,
  },
  {
    label: '发布',
    key: 2,
  },
];

function SharePublish(props: PopoverProps) {
  const { children } = props;

  const { user } = useUserInfoStore.getState();
  // 控制分享弹框显示隐藏
  const [showPopover, setShowPopover] = useState(false);
  const shareRef = useRef<ShareInstance>(null as unknown as ShareInstance);
  const [isClose, setIsClose] = useState(false);
  // 控制切换分享发布
  const [selectKey, setSelectKey] = useState(1);
  const [permCode, setPermCode] = useState<any>([]);
  // wld-tree 控制切换发布
  const { updateIsShowPublish, docBlockId, docSpaceId } = useDocStore();

  const onSelectKey = (val: number) => {
    setSelectKey(val);
    if (val === 2) {
      // updateViewDoc(true);
      // 显示发布笔记
      updateIsShowPublish(true);
      setShowPopover(false);
      setSelectKey(1);
    }
  };

  const { run } = useRequest(getPagePer, {
    manual: true,
    onSuccess: (res) => {
      setPermCode(res.perCodes);
    },
  });

  const onInviteCollaborator = async (value = []) => {
    setIsClose(true);
    const res = await permMemberCollaboratorListApi({ blockId: docBlockId || '' });
    const defaults =
      res.list && res.list.length
        ? res.list.map((item) => ({
            type: item.memberType === 2 ? 'departments' : 'users',
            memberId: item.memberId,
            id: item.memberId,
            label: item.name,
            closable: false,
          }))
        : [];
    // @ts-ignore
    // eslint-disable-next-line no-unsafe-optional-chaining
    // const { selectUsers } = window?.microApp?.getData();
    selectUsers({
      title: `邀请协作者`,
      companyId: user.user.companyId,
      onlyCompany: true,
      defaults: [...defaults, ...value] as Item[],
      disables: [...defaults, ...value] as Item[],
      // closable: false,
      selectMemberType: 'memberId',
      searchIsFilter: null,
      onConfirm: (instance: any, close: any) => {
        const ids = res.list.map((item) => item.memberId);
        const data = instance.getList();
        const arr = data.filter((item: any) => !ids.includes(item.id));
        close();
        setPerm({
          type: 'collaborator',
          title: '邀请协作者',
          addType: 0,
          spaceId: docSpaceId,
          blockId: docBlockId,
          defaultData: arr,
          permCode,
          onInVite: (val: any) => {
            onInviteCollaborator(val);
          },
          onConfirm: () => {
            shareRef.current.onRefresh();
            setIsClose(false);
          },
          onCancel: () => {
            setIsClose(false);
          },
        });
      },
      onCancel: () => {
        setIsClose(false);
      },
    });
  };

  const onVisibleChange = (val: boolean) => {
    if (isClose) {
      return;
    }
    setShowPopover(val);
  };

  const tabListArr = useMemo(() => {
    if (permCode.includes('BS_001_001') || permCode.includes('BS_001_002')) {
      return tabList;
    }
    return tabList.filter((item) => item.key === 1);
  }, [permCode]);

  useEffect(() => {
    if (showPopover) {
      run({ blockId: docBlockId });
    }
  }, [docBlockId, run, showPopover]);
  // 分享弹出框内容
  const content = (
    <div className={styles.content}>
      <div className={styles.title}>
        <div className={styles.tabs}>
          {tabListArr.map((item) => {
            return (
              <span
                key={item.key}
                className={classNames(styles.item, { [styles.itemActive]: selectKey === item.key })}
                onClick={() => onSelectKey(item.key)}
              >
                {item.label}
              </span>
            );
          })}
        </div>
        {selectKey === 1 &&
          (permCode.includes('BS_001_001') || permCode.includes('BS_001_002')) && (
            <div className={styles.invite} onClick={() => onInviteCollaborator([])}>
              邀请协作者
            </div>
          )}
      </div>
      {selectKey === 1 && (
        <Share
          // 权限 BS_001_001
          permCode={permCode}
          // 空间id
          blockId={docBlockId}
          // 用来刷新数据，每次打开弹窗请求一次数据
          showPopover={showPopover}
          ref={shareRef}
          onIsClose={setIsClose}
        />
      )}
      {/* {selectKey === 2 && <Publish permCode={permCode} blockId={blockId} />} */}
    </div>
  );

  return (
    <Popover
      {...props}
      content={content}
      // trigger={showPopover ? 'hover' : 'click'}
      trigger="click"
      placement="bottomRight"
      overlayClassName={styles.popover}
      visible={showPopover}
      onVisibleChange={onVisibleChange}
    >
      {/* 分享 */}
      <div className={styles.share}>{children}</div>
    </Popover>
  );
}

export default SharePublish;
