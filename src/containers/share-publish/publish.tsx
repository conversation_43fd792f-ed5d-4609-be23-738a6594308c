import { Button, message } from '@echronos/antd';
import { CopyOutlined } from '@echronos/icons';
import { getPageData, setPageData } from '@echronos/editor/dist/core';
import { onWindowOpen } from '@/utils/utils';
import { pushDocPage, pushDocPageCancel } from '@/apis';
import { useEffect, useState } from 'react';
import copy from 'copy-to-clipboard';
import { getFrontSiteUrl } from '@/utils/env';
import styles from './publish.module.less';

interface PublishProps {
  blockId: string;
  permCode: string[];
}

function Publish({ blockId, permCode }: PublishProps) {
  // 网站信息
  const page = getPageData();
  const urlPath = getFrontSiteUrl();
  const pageURL = `${urlPath}/doc/${page.pageId}`;
  const [isPublish, setIsPublish] = useState(false);
  const isPerm = permCode.includes('BS_001_001') || permCode.includes('BS_001_002');

  const onPublish = () => {
    pushDocPage({ rootBlockId: blockId })
      .then(() => {
        message.success('发布成功');
        setPageData({ ...page, publishStatus: 1 });
        setIsPublish(true);
      })
      .catch(() => {
        message.success('发布失败');
      });
  };

  const onCopy = () => {
    copy(pageURL);
    message.success('复制成功');
  };

  const onCancel = () => {
    pushDocPageCancel({ rootBlockId: blockId as string })
      .then(() => {
        message.success('取消成功');
        setPageData({ ...page, publishStatus: 0 });
        setIsPublish(false);
      })
      .catch(() => {
        message.success('取消失败');
      });
  };

  const onLookSite = () => {
    onWindowOpen(pageURL);
  };

  useEffect(() => {
    setIsPublish(!!page.publishStatus);
  }, [page.publishStatus]);

  return (
    <div className={styles.box}>
      {!page.publishStatus && !isPublish ? (
        <div>
          <div className={styles.publishText}>
            发布此页面到网络,所有人都可以通过链接访问你的网站。
          </div>
          {isPerm && (
            <Button className={styles.publishBtn} type="primary" onClick={onPublish}>
              发布到网络
            </Button>
          )}
        </div>
      ) : (
        <div className={styles.publish}>
          <img
            className={styles.publishImg}
            src="https://img.huahuabiz.com/user_files/202463/1717385383610365.png"
            alt=""
          />
          <div>发布成功</div>
          <div className={styles.publishTip}>此页面已在网上上线</div>
          <div className={styles.publishLink}>
            <span className={styles.publishLinkText}>{pageURL}</span>
            <CopyOutlined className={styles.copy} onClick={onCopy} />
          </div>
          <div className={styles.footer}>
            {isPerm && (
              <Button className={styles.cancelBtn} onClick={onCancel}>
                取消发布
              </Button>
            )}
            <Button type="primary" className={styles.btn} onClick={onLookSite}>
              查看网站
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Publish;
