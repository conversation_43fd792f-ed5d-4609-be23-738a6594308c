@import '@echronos/react/less/index';

.no-wrap(@W:180px) {
  max-width: @W;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content {
  max-height: 364px;
  padding: 0 20px;
  overflow: auto;
}

.collapse {
  background-color: #fff;

  :global {
    .ant-collapse-item {
      border-bottom: none !important;
    }

    .ant-collapse-header {
      padding: 0 0 16px 0 !important;
      align-items: center !important;
    }

    .ant-collapse-arrow {
      font-size: 10px !important;
      color: #040919;
    }

    .ant-collapse-content > .ant-collapse-content-box {
      padding: 0;
    }
  }
}

.header {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 8px;
}

.headerImg {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 8px;
}

.headerContent {
  flex: 1;
  display: flex;
  justify-content: space-between;
}

.headerLabel {
  margin-bottom: 4px;
}

.headerLabelTitle {
  font-weight: 600;
  margin-bottom: 2px;
  font-size: 14px;
  color: #040919;
  .no-wrap();
}

.headerLabelName {
  font-size: 12px;
  color: #b1b3be;
  .no-wrap();
}

.headerLabelTitleText {
  margin-right: 4px;
}
.headerLabelTitleTextOth {
  margin-right: 4px;
  font-size: 14px;
  font-weight: normal;
  line-height: normal;
  color: #040919;
  .no-wrap();
}

.headerValue {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: auto;
}

.headerValueRole {
  margin-bottom: 4px;
  text-align: right;
}

.headerValueText {
  font-family: '苹方-简';
  font-size: 12px;
  margin-bottom: 2px;
  color: #b1b3be;
  .no-wrap();
}

.headerValueDefault {
  font-family: ' 苹方-简';
  font-size: 12px;
  color: #b1b3be;
}

.itemSet {
  text-align: right;
  cursor: pointer;
}

.list {
  padding-left: 58px;
  padding-bottom: 16px;
}

.item {
  display: flex;

  &:not(:last-child) {
    margin-bottom: 16px;
  }
}

.itemImg {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.cell {
  //padding-left: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cellImg {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 8px;
}

.cellLabel {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.cellLabelText {
  margin-right: 8px;
  .no-wrap();
  // .text-overflow(1);
}

.other {
  padding: 12px 0;
  font-size: 12px;
  color: #888b98;
}

.shareEnter {
  width: 170px;
  background-color: #eee;
  border-radius: 5px;
  cursor: pointer;
  span {
    padding: 0 8px;
    font-size: 0.7em;
    margin-right: 10px;
    line-height: 20px;
  }
}
.footer {
  border-top: 1px solid #f3f3f3;
  padding: 12px 20px 20px 20px;
  margin-top: 12px;
}

.footerTop {
  margin-bottom: 12px;
  font-size: 12px;
  color: #888b98;
}

.footerText {
  display: flex;
}

.footerBtn {
  display: flex;
}

.footerTip {
  font-size: 12px;
  color: #888b98;
  padding: 8px 0;
}

.footerText {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footerCopy {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  margin-left: 12px;
  border-radius: 50%;
  border: 1px solid #f3f3f3;
  color: #008cff;
  cursor: pointer;

  &:hover {
    background: rgba(177, 179, 190, 0.2);
  }
}

.footerCopyIcon {
  font-size: 16px;
  color: #000;
}

.send {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 118px;
  height: 32px;
  //margin-right: 12px;
  border-radius: 20px;
  border: 1px solid #f3f3f3;
  cursor: pointer;

  &:hover {
    background: rgba(177, 179, 190, 0.2);
  }
}

.sendImg {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.dropdownMenu {
  :global {
    .ant-dropdown-menu-item:hover {
      background-color: rgba(177, 179, 190, 0.2);
    }
  }
}
