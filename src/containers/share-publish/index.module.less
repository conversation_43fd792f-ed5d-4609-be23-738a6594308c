.popover {
  border-radius: 10px;
  z-index: 1900 !important;

  :global {
    .ant-popover-inner {
      border-radius: 10px;
    }

    .ant-popover-inner-content {
      border-radius: 10px;
      width: 400px;
      padding: 20px 0 0 0 !important;
      z-index: 1900 !important;
      box-shadow: -8px 8px 24px 0 rgba(2, 9, 58, 0.16);
    }
  }
}

.share {
  cursor: pointer;
}

.content {
  display: flex;
  flex-direction: column;
}

.title {
  display: flex;
  justify-content: space-between;
  padding: 0 20px 20px 20px;
}

.tabs {
  display: flex;
}

.item {
  margin-right: 24px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
}

.itemActive {
  position: relative;
  color: #008cff;

  &:after {
    content: '';
    position: absolute;
    width: 12px;
    height: 3px;
    border-radius: 3px;
    background-color: #008cff;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
  }
}

.invite {
  font-size: 16px;
  color: #008cff;
  cursor: pointer;
}
