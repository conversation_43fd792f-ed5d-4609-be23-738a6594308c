/* eslint-disable */
import { forwardRef, useContext, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { Tooltip } from '@echronos/antd';
import selectUsers from '@/components/select-users';
import { message, Modal } from 'antd';
import Icon from '@echronos/echos-icon';
// import Icon from '@/components/icon';
import {
  permMemberListAllApi,
  PermMemberListAllData,
  permMemberCollaboratorChangeApi,
  permMemberCollaboratorRemoveApi,
  postSendUser,
} from '@/apis';
import { onCopyLink } from '@/utils/handle';
import { UserinfoContext } from '@echronos/react';
import { uniq } from 'lodash';
import { getFrontSiteUrlAi } from '@/utils/env';
import { DownSelect } from '@/components';
// import shareApplet from './share-appler';
import styles from './share.module.less';

const iconAdmin = 'https://img.huahuabiz.com/user_files/2024615/1718414421662779.png';
const iconEdit = 'https://img.huahuabiz.com/user_files/2024615/1718414485219291.png';
const defaultLogo = 'https://img.huahuabiz.com/PC/static/img/default_avatar/24.png';

export interface ShareInstance {
  onRefresh: () => void;
  owner: () => any;
}

interface ShareProps {
  blockId: string;
  showPopover: boolean;
  onIsClose: (val: boolean) => void;
  permCode: string[];
}

const menuItems = [
  { key: 1, label: '可管理', roleId: 28 },
  { key: 2, label: '可编辑', roleId: 29 },
  { key: 3, label: '可查看', roleId: 30 },
  { key: 4, label: '移除成员', roleId: 0 },
];

const Share = forwardRef<ShareInstance, ShareProps>(
  ({ blockId, showPopover, onIsClose, permCode }, ref) => {
    const { user } = useContext(UserinfoContext);
    const [info, setInfo] = useState<PermMemberListAllData>({
      collaborators: [],
      owner: {
        userLogo: '',
        name: '',
        roleId: 0,
        roleName: '',
      },
      spaceEditors: [],
      spaceMembers: [],
      totalCount: 0,
    });
    const block = useParams();
    const blockIdVal = blockId || block.blockId || '';

    const urlPath = getFrontSiteUrlAi();
    const pageURL = `${urlPath}/?docId=${blockIdVal}&openWldTree=1&action=openDoc`;

    // 其他协作者列表
    const renderSpaceMembers = useMemo(() => {
      const arr = info.collaborators.filter((item) => item.id !== user.id);
      return arr;
    }, [info, user.id]);

    const { run } = useRequest(permMemberListAllApi, {
      manual: true,
      defaultParams: [{ blockId: blockIdVal }],
      onSuccess: (res) => {
        setInfo(res);
      },
    });

    const getKeys = (val: number) => {
      const info = menuItems.find((item) => item.roleId === val);
      return info?.key;
    };

    // 改变角色
    const onChangeRole = (val: number, memberRoleId: number) => {
      const info = menuItems.find((item) => item.key === val);
      permMemberCollaboratorChangeApi({
        memberRoleId,
        roleId: info?.roleId || 0,
        blockId: blockIdVal,
      }).then(() => {
        message.success('修改成功');
        run({ blockId: blockIdVal });
      });
    };

    // 移除协作者
    const onRemoveMember = (val: number) => {
      onIsClose(true);
      Modal.confirm({
        title: '提示',
        icon: '',
        centered: true,
        content: '确认移除该协作者？',
        onOk: () => {
          permMemberCollaboratorRemoveApi({ blockId: blockIdVal, memberRoleId: val }).then(() => {
            run({ blockId: blockIdVal });
            message.success('移除成功');
            onIsClose(false);
          });
        },
        onCancel: () => onIsClose(false),
      });
    };

    const onSend = () => {
      // @ts-ignore
      // eslint-disable-next-line no-unsafe-optional-chaining
      // const { selectUsers } = window?.microApp?.getData();
      selectUsers({
        title: `发送到聊天`,
        closable: false,
        onConfirm: (instance: any, close: any) => {
          const data = instance.getList();
          let userIds: number[] = [];
          const groupIds: number[] = [];
          data.forEach((item: any) => {
            if (item.type === 'groups') {
              groupIds.push(item.id);
            } else {
              if (item.id) {
                userIds.push(item.id);
              }
              if (item.userIds && item.userIds.length) {
                userIds = userIds.concat(item.userIds);
              }
            }
          });

          postSendUser({
            userIds: uniq(userIds),
            groupIds,
            blockId: blockIdVal,
            url: pageURL,
          }).then(() => {
            close();
            message.success('转发成功');
          });
        },
      });
    };

    const onShareWx = () => {
      // shareApplet({ blockId: blockIdVal });
    };

    const onMoreMenu = (val: number, memberRoleId: number, current: number) => {
      switch (val) {
        case 1:
        case 2:
        case 3: // 跟换角色
          if (val === current) {
            return;
          }
          onChangeRole(val, memberRoleId);
          break;
        case 4: // 移除成员
          onRemoveMember(memberRoleId);
          break;
        default:
          break;
      }
    };

    useImperativeHandle(
      ref,
      () => ({
        onRefresh: () => run({ blockId: blockIdVal }),
        owner: () => info.owner,
      }),
      [blockIdVal, info.owner, run]
    );

    useEffect(() => {
      if (showPopover) {
        run({ blockId: blockIdVal });
      }
    }, [run, blockId, showPopover, blockIdVal]);

    return (
      <div>
        <div className={styles.content}>
          {/* 循环 管理分享权限 */}
          {info.spaceMembers.map((item) => (
            <div className={styles.header} key={item.id}>
              <img
                className={styles.headerImg}
                src={item.memberId === 28 ? iconAdmin : iconEdit}
                alt=""
              />
              <div className={styles.headerContent}>
                <div className={styles.headerLabel}>
                  <div className={styles.headerLabelTitle} title={item.name}>
                    {/* 权限名字 */}
                    {item.name}
                  </div>
                  <div className={styles.headerLabelName} title={item.spaceName}>
                    {/* 空间名字 */}
                    {item.spaceName}
                  </div>
                </div>
                <div className={styles.headerValue}>
                  {permCode.includes('BS_001_001') && item.memberId !== 28 ? (
                    <DownSelect
                      placement="bottom"
                      items={menuItems.filter((item) => item.key !== 4)}
                      currentKey={getKeys(item.roleId) || 1}
                      onChangeKey={(val, current) => onMoreMenu(val, item.memberRoleId, current)}
                    />
                  ) : (
                    <div className={styles.itemSet}>
                      <span>{item.roleName}</span>
                    </div>
                  )}
                  {!item.status && <div className={styles.headerValueDefault}>继承父级页面</div>}
                </div>
              </div>
            </div>
          ))}
          <div className={styles.cell}>
            <div className={styles.cellLabel}>
              <img className={styles.cellImg} src={info.owner?.userLogo || defaultLogo} alt="" />
              <div className={styles.cellLabelText} title={info.owner?.name}>
                {info.owner?.name}
              </div>
              <img src="https://img.huahuabiz.com/user_files/202463/1717384077181388.png" alt="" />
            </div>
            <div>{info.owner?.roleName}</div>
          </div>
          {renderSpaceMembers && !!renderSpaceMembers.length && (
            <>
              <div className={styles.other}>其他协作人员</div>
              {renderSpaceMembers.map((item) => (
                <div className={styles.header} key={item.id}>
                  <img
                    className={styles.headerImg}
                    src={
                      item.userLogo ||
                      'https://img.huahuabiz.com/PC/static/img/default_avatar/24.png'
                    }
                    alt=""
                  />
                  <div className={styles.headerContent}>
                    <div className={styles.headerLabel}>
                      <div className={styles.headerLabelTitle}>
                        <span className={styles.headerLabelTitleTextOth} title={item.name}>
                          {item.name}
                        </span>
                        {item.memberType === 2 && (
                          <img
                            src="https://img.huahuabiz.com/user_files/202461/1717205475078231.png"
                            alt=""
                          />
                        )}
                      </div>
                      <div className={styles.headerValueText} title={item.companyName}>
                        {item.companyName}
                      </div>
                    </div>
                    <div className={styles.headerValue}>
                      {permCode.includes('BS_001_001') ? (
                        <DownSelect
                          placement="bottom"
                          items={menuItems}
                          currentKey={getKeys(item.roleId) || 1}
                          onChangeKey={(val, current) =>
                            onMoreMenu(val, item.memberRoleId, current)
                          }
                        />
                      ) : (
                        <div className={styles.itemSet}>
                          <span>{item.roleName}</span>
                        </div>
                      )}
                      {!item.status && (
                        <div className={styles.headerValueDefault}>继承父级页面</div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </>
          )}
          {/* <div className={styles.other}>链接分享范围</div>
          <div className={styles.shareEnter}>
            <span>仅协作者可通过链接访问</span>
            <RightOutlined />
          </div> */}
        </div>
        {/* 分享底部显示内容 */}
        {(permCode.includes('BS_001_001') || permCode.includes('BS_001_002')) && false ? (
          <div className={styles.footer}>
            <div className={styles.footerTop}>链接分享范围</div>
            <div className={styles.footerText}>
              <div>获得链接的人均可查看</div>
              <div className={styles.footerBtn}>
                <div className={styles.send} onClick={onSend}>
                  <img
                    className={styles.sendImg}
                    src="https://img.huahuabiz.com/user_files/2024713/1720854676953671.png"
                    alt=""
                  />
                  <span>发送到聊天</span>
                </div>
                <span
                  className={styles.footerCopy}
                  onClick={onShareWx}
                  style={{ height: '0px', width: '0px', overflow: 'hidden' }}
                >
                  <img
                    src="https://img.huahuabiz.com/user_files/2024824/1724465472851761.png"
                    alt=""
                  />
                </span>
                <Tooltip title="复制链接">
                  <span
                    className={styles.footerCopy}
                    onClick={() => {
                      onCopyLink();
                    }}
                    style={{ height: '0px', width: '0px', overflow: 'hidden' }}
                  >
                    <Icon name="lianjian" className={styles.footerCopyIcon} />
                  </span>
                </Tooltip>
              </div>
            </div>
          </div>
        ) : (
          <div style={{ height: '20px' }} />
        )}
      </div>
    );
  }
);

export default Share;
