import { ReactNode, useRef, useState, MouseEvent } from 'react';
import { Popover, Divider, message, Modal, Tooltip } from '@echronos/antd';
import classNames from 'classnames';
import { PermSpaceListData, updateDocPageName } from '@/apis';
import Icon from '@echronos/echos-icon';
import { closeSendCommands } from '@echronos/editor/dist/core';
import { useNavigate, useParams } from 'react-router-dom';
import renamePop from '@/components/rename/rename';
import deleteDocPage from '@/apis/doc-page/delete-doc-page';
import { onCopyLink, onNavBlock, removeTree, updateTree } from '@/utils/handle';
import useDocState from '@/store/useDocStore';
import useHeadStore from '@/store/head';
import useSpaceStore from '@/store/space';
import { cloneDeep } from 'lodash';
import styles from '../space-menu/index.module.less';

interface PageMenuProps {
  /** 我的空间 || 共享给我的空间 */
  // isOwner: boolean;
  type: string;
  spaceId: string;
  spaceInfo: PermSpaceListData;
  children: ReactNode;
  onCloseHandle: () => void;
}

const menu = [
  {
    key: 'rename',
    label: '重命名',
    icon: 'edit_line',
    code: ['BS_001_001', 'BS_001_002'],
  },
  {
    key: 'copy',
    label: '复制链接',
    icon: 'copy_line',
    code: '',
  },
];

const defaultPagePng = 'https://img.huahuabiz.com/user_files/2024625/1719301693061916.png';

function PageMenu({
  type,
  spaceId,
  spaceInfo,
  onCloseHandle,
  ...props
}: PageMenuProps & { logoUrl: string }) {
  const space = useSpaceStore();
  const head = useHeadStore();
  const navigate = useNavigate();
  const moreRef = useRef(null);
  const { session_id: blockId } = useParams();
  const [showPopover, setShowPopover] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const { indexDBProvider, setIndexDBProvider, socketProvider, setSocketProvider } = useDocState();

  const params = useParams();

  const onClickIcon = (e: MouseEvent) => {
    e.stopPropagation();
    setShowPopover(true);
    setShowTooltip(false);
  };

  // 更新列表
  const onUpdateList = (val: string, name = '', logo = '') => {
    const arr =
      type === 'group' ? cloneDeep(space.spaceGroupList) : cloneDeep(space.spaceShareList);
    if (val === 'del') {
      if (blockId === spaceInfo.blockId) {
        onNavBlock({
          groupList: type === 'group' ? arr : cloneDeep(space.spaceGroupList),
          shareList: type === 'share' ? arr : cloneDeep(space.spaceShareList),
          blockInfo: spaceInfo,
          navigate,
          type,
        });
      }
      removeTree(arr, spaceInfo.key);
    }
    if (val === 'update') {
      updateTree(arr, 'blockId', spaceInfo.blockId, 'name', name);
      updateTree(arr, 'blockId', spaceInfo.blockId, 'logo', logo);
    }
    if (type === 'group') {
      space.setSpaceGroupList(cloneDeep(arr));
    } else {
      space.setSpaceShareList(cloneDeep(arr));
    }
    // 更新头部导航
    const headerArr = space.headerNavList;
    headerArr.forEach((item) => {
      const items = item;
      if (item.key === spaceInfo.key) {
        items.name = name;
        items.logo = logo || defaultPagePng;
      }
    });
    space.setHeaderNavList(cloneDeep(headerArr));
    localStorage.setItem('HEADER_NAV_LIST', JSON.stringify(cloneDeep(headerArr)));
    if (spaceInfo.blockId === blockId) {
      const headInfo = head.headData;
      head.setHeadData({
        ...headInfo,
        title: name,
        avatar: {
          url: logo,
          type: 'icon',
        },
      });
    }
  };

  // 重命名
  const onRename = () => {
    // @ts-ignore
    const rect = moreRef.current?.getBoundingClientRect();
    renamePop().open({
      pos: {
        top: rect.top - 80,
        left: rect.left,
      },
      pageName: spaceInfo.name,
      logoUrl: spaceInfo.logo,
      renameChange: (val, logo) => {
        updateDocPageName({
          spaceId,
          blockId: spaceInfo.blockId,
          attrs: {
            pageName: val,
            logo,
          },
        }).then(() => {
          message.success('更改成功');
          onUpdateList('update', val, logo);
          if (spaceInfo.blockId === blockId) {
            // 协同标题
            if (socketProvider) {
              const headInfo = head.headData;
              socketProvider.ws?.send(
                JSON.stringify({
                  type: 'EDIT',
                  content: {
                    head: {
                      ...headInfo,
                      title: val,
                      avatar: {
                        url: logo,
                        type: 'icon',
                      },
                    },
                    blockId,
                  },
                  message: '',
                })
              );
            }
          }
        });
      },
    });
  };

  // 删除页面
  const onDeletePage = () => {
    Modal.confirm({
      title: '提示',
      icon: '',
      centered: true,
      content: '确定删除当前页面吗？删除当前页面后，页面下所有子页面也将一同被删除',
      onOk: () => {
        deleteDocPage({ spaceId, blockId: spaceInfo.blockId })
          .then(() => {
            message.success('删除成功');
            // 关闭 socket 链接并删除本地缓存
            if (params.blockId === spaceInfo.blockId) {
              if (socketProvider) {
                socketProvider.isInitiativeCloseStatus = true;
                socketProvider.destroy();
                setSocketProvider(null);
              }
              closeSendCommands();
              // 删除本地缓存的文档数据
              indexDBProvider?.clearData();
              setIndexDBProvider(null);
            } else {
              indexedDB.deleteDatabase(`doc-${spaceInfo.blockId}`);
            }
          })
          .then(() => {
            onUpdateList('del');
          });
      },
    });
  };

  const onApaceSet = (e: MouseEvent, val: string) => {
    e.stopPropagation();
    setShowPopover(false);
    onCloseHandle();
    switch (val) {
      case 'rename':
        onRename();
        break;
      case 'copy':
        onCopyLink(spaceInfo.blockId);
        break;
      case 'del':
        onDeletePage();
        break;
      default:
        break;
    }
  };

  const content = (
    <>
      {menu.map((item) => {
        return (
          (spaceInfo.perCodes.some((code) => item.code.includes(code)) || !item.code) && (
            <div
              ref={moreRef}
              className={styles.item}
              key={item.key}
              onClick={(event) => onApaceSet(event, item.key)}
            >
              <Icon className={styles.itemIcon} name={item.icon} />
              <span>{item.label}</span>
            </div>
          )
        );
      })}
      {spaceInfo.perCodes.includes('BS_001_001') && (
        <div className={styles.divider}>
          <Divider />
        </div>
      )}
      {spaceInfo.perCodes.includes('BS_001_001') && (
        <div
          className={classNames(styles.item, styles.itemDel)}
          onClick={(event) => onApaceSet(event, 'del')}
        >
          <Icon
            className={classNames(styles.itemIcon, styles.itemIconDel)}
            name="delete_trash_line"
          />
          <span>删除页面</span>
        </div>
      )}
    </>
  );

  return (
    <Popover
      trigger={showPopover ? 'hover' : 'click'}
      placement="bottomLeft"
      overlayClassName={styles.popover}
      content={content}
      visible={showPopover}
      onVisibleChange={(val) => {
        setShowPopover(val);
        if (!val) {
          onCloseHandle();
        }
      }}
    >
      <Tooltip placement="bottom" title="更多操作" visible={showTooltip}>
        <div
          role="button"
          tabIndex={-1}
          onClick={onClickIcon}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
        >
          {props.children}
        </div>
      </Tooltip>
    </Popover>
  );
}

export default PageMenu;
