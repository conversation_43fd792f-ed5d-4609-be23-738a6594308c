@import '@echronos/react/less/index';
@import '../space-create/space-create.module.less';

.modal {
  :global {
    .ant-modal-header {
      display: none !important;
    }

    .ant-modal-close-x {
      display: none !important;
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  padding: 20px 0;
  font-size: 16px;
  font-weight: 600;
}

.headerText {
  color: #008cff;
  cursor: pointer;
}

.box {
  width: 660px;
  height: 170px;
  overflow: auto;
  border: 1px solid #008cff;
  padding: 5px 12px;
  border-radius: 6px;
  display: flex;
  flex-wrap: wrap;
}

.item {
  display: flex;
  height: 24px;
  line-height: 24px;
  padding: 0 8px;
  margin-right: 8px;
  border-radius: 4px;
  background: #f5f6fa;
}

.itemText {
  max-width: 96px;
  .text-overflow();
}

.itemClose {
  cursor: pointer;
  margin-left: 4px;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding: 20px 0;
}

.footerPerm {
  display: flex;
}

.btnCancel {
  margin-right: 16px;
}
