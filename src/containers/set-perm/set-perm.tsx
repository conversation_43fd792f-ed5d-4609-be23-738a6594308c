import { useEffect, useMemo, useState } from 'react';
import { Modal, ModalProps, Button, message } from 'antd';
import Icon from '@echronos/echos-icon';
import { permMemberAddApi, permMemberCollaboratorApi } from '@/apis';
import DownSelect from '@/components/down-select';
import { isFunction } from 'lodash';
import styles from './set-perm.module.less';

const items = [
  { key: 1, label: '可管理', roleId: 28 },
  { key: 2, label: '可编辑', roleId: 29 },
  { key: 3, label: '可查看', roleId: 30 },
];

interface MemberData {
  id: number;
  label: string;
  type: string;
  memberId: number;
}

export interface SetPermProps extends ModalProps {
  type?: string;
  title: string;
  spaceId: string;
  blockId?: string;
  addType: number;
  defaultData: MemberData[];
  permCode: string[];
  // eslint-disable-next-line no-unused-vars
  onInVite: (val: MemberData[]) => void;
  onConfirm?: () => void;
}

function SetPerm({
  type,
  title,
  spaceId,
  blockId,
  addType,
  defaultData,
  permCode,
  onInVite,
  onConfirm,
  ...props
}: SetPermProps) {
  const [list, setList] = useState([...defaultData]);
  const [currentKey, setCurrentKey] = useState(3);

  const onDelete = (val: number) => {
    setList(list.filter((item) => item.id !== val));
  };

  const onClose = () => {
    if (isFunction(props.onCancel)) {
      // @ts-ignore
      props.onCancel();
    }
  };

  const onSubmit = () => {
    const info = items.find((item) => item.key === currentKey);
    const fn = type === 'collaborator' ? permMemberCollaboratorApi : permMemberAddApi;
    const params =
      type === 'collaborator'
        ? {
            spaceId,
            blockId: blockId || '',
            roleId: Number(info?.roleId),
            memberList: list.map((item) => ({
              memberId: item.type === 'departments' ? item.id : item.memberId,
              memberType: item.type === 'departments' ? 2 : 0,
              roleId: 29,
            })),
          }
        : {
            spaceId,
            roleId: Number(info?.roleId),
            addType,
            memberList: list.map((item) => ({
              memberId: item.type === 'departments' ? item.id : item.memberId,
              memberType: item.type === 'departments' ? 2 : 0,
              roleId: Number(info?.roleId),
            })),
          };
    // @ts-ignore
    fn({ ...params }).then(() => {
      onClose();
      message.success('添加成功');
      onConfirm?.();
    });
  };

  const itemsArr = useMemo(() => {
    const arr = permCode || [];
    if (arr.includes('BS_001_001')) {
      return items;
    }
    if (arr.includes('BS_001_002')) {
      return items.filter((item) => item.key !== 1);
    }
    if (arr.includes('BS_001_003')) {
      return items.filter((item) => item.key === 3);
    }
    return [];
  }, [permCode]);

  useEffect(() => {
    setList(defaultData);
  }, [defaultData]);

  return (
    <Modal className={styles.modal} title={title} {...props} centered width={700} footer={null}>
      <div className={styles.header}>
        <div>{title}</div>
        <div
          className={styles.headerText}
          onClick={() => {
            onClose();
            onInVite(list);
          }}
        >
          邀请成员
        </div>
      </div>
      <div className={styles.box}>
        {list.map((item) => {
          return (
            <div key={item.id} className={styles.item}>
              <span className={styles.itemText} title={item.label}>
                {item.label}
              </span>
              <Icon
                name="close_line"
                className={styles.itemClose}
                onClick={() => onDelete(item.id)}
              />
            </div>
          );
        })}
      </div>
      <div className={styles.footer}>
        <div className={styles.footerPerm}>
          <span>统一设置权限为：</span>
          <DownSelect
            currentKey={currentKey}
            items={itemsArr}
            placement="top"
            onChangeKey={setCurrentKey}
          />
        </div>
        <div>
          <Button
            className={styles.btnCancel}
            onClick={() => {
              onClose();
              // onInVite(list);
            }}
          >
            取消
          </Button>
          <Button type="primary" onClick={onSubmit} disabled={!list.length}>
            确定
          </Button>
        </div>
      </div>
    </Modal>
  );
}

SetPerm.defaultProps = {
  type: 'collaborator',
  blockId: '',
  onConfirm: () => {},
};

export default SetPerm;
