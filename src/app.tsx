import { useContext, useEffect, useState } from 'react';
import { useRoutes } from 'react-router-dom';
import { Spin } from '@echronos/antd';
import { getUserInfo } from '@/apis';
import {
  CLIENT,
  emitBridgeEvent,
  initPermission,
  userInit,
  setPermissionMap,
  UserPermission,
  setToken,
  setHttpConfig,
} from '@echronos/core';
import { setLazyFallback, UserinfoContext } from '@echronos/react';
import useAgent from '@/store/useAgent';
import SpinBox from './components/spin-box';
import useUserInfoStore from './store/user-info';
import getIframeToParentChannel from './service/iframe-to-parent-channel';
import routes from './routes';
import './app.less';

function App() {
  const router = useRoutes(routes);
  const { resetAgent } = useAgent();
  const user = useUserInfoStore.getState();
  const { updateUserStore } = useContext(UserinfoContext);
  const [initial, setInitial] = useState(false);

  const initUserPermission = () => {
    const microData = window.microApp?.getData();
    // eslint-disable-next-line no-underscore-dangle
    if (CLIENT && window.__MICRO_APP_ENVIRONMENT__ && microData && microData.permissionMap) {
      setPermissionMap(microData.permissionMap as UserPermission);
      return Promise.resolve();
    }
    // eslint-disable-next-line no-underscore-dangle
    return initPermission(!(CLIENT && window.__MICRO_APP_ENVIRONMENT__));
  };

  const init = () => {
    initUserPermission().then(() => {
      userInit()
        .then((result) => {
          if (result?.user.id) {
            // @ts-ignore
            updateUserStore(result);
            user.setUser({
              user: { ...result.user },
              shop: { ...result.shop },
              company: {
                ...result.company,
              },
            });
          }
        })
        .finally(() => {
          setInitial(true);
          emitBridgeEvent('micro-app:mounted');
        });
    });
  };

  useEffect(() => {
    if (window.microApp?.getData()) {
      init();
    } else if (window.top !== window.self) {
      getIframeToParentChannel().invoke('getToken', {
        success: (e: any) => {
          // eslint-disable-next-line no-console
          if (e.data) {
            setHttpConfig({
              headers: {
                common: {
                  satype: import.meta.env.BIZ_API_SA_TYPE || '',
                },
                Authorization: e.data,
              },
            });
            getUserInfo().then((res: any) => {
              user.setUser({
                user: { ...res },
                shop: { ...res.memberVO },
                company: {
                  ...res.companyVO,
                },
              });
            });
            setToken(e.data).then(() => {
              init();
            });
          }
        },
      });
      emitBridgeEvent('micro-app:mounted');
    } else {
      setInitial(true);
    }

    setLazyFallback(<SpinBox />);

    return () => {
      resetAgent();
    };
  }, []); // eslint-disable-line

  return initial ? (
    router
  ) : (
    <Spin spinning>
      <div style={{ width: '100%' }} />
    </Spin>
  );
}

export default App;
