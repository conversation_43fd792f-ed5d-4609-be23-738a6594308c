import { createContext } from 'react';

interface SiderProviderProp {
  onCollapsed: () => void;
  Collapsed: boolean;
  breakPoint: boolean;
  isMovingCollapsed: boolean; // 是否正在移动左侧栏
  CanFloatSider: boolean; //  是否为悬浮状态
  FloatMouseEnterEvent: () => void;
  FloatMouseLeaveEvent: () => void;
  getSiderWidth: () => number;
}
const SiderProvider = createContext<SiderProviderProp>({
  onCollapsed: () => false,
  Collapsed: false,
  breakPoint: false,
  CanFloatSider: false,
  isMovingCollapsed: false,
  getSiderWidth: () => 0,
  FloatMouseEnterEvent: () => {},
  FloatMouseLeaveEvent: () => {},
});
export default SiderProvider;
