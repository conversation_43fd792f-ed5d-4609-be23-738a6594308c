// eslint-disable-next-line import/no-extraneous-dependencies
import { v4 as uuidv4 } from 'uuid';
import { message } from '@echronos/antd';
import { headCoverEmojiJSON, headCoverPicturesJSON } from '@echronos/millet-ui';
import { getDomain } from '@/apis';

export const generateUniqueId = () => uuidv4();

export function isFunction(fun: FunctionConstructor) {
  return Object.prototype.toString.call(fun) === '[object Function]';
}

export function createRect(rect: DOMRect | null): DOMRect {
  if (rect == null) {
    return {
      left: 0,
      top: 0,
      width: 0,
      height: 0,
      bottom: 0,
      right: 0,
      x: 0,
      y: 0,
    } as DOMRect;
  }
  const newRect = {
    ...rect,
    left: rect.left + document.body.scrollLeft,
    top: rect.top + document.body.scrollTop,
    width: rect.width,
    height: rect.height,
    bottom: 0,
    right: 0,
  };
  newRect.bottom = newRect.top + newRect.height;
  newRect.right = newRect.left + newRect.width;
  return newRect;
}

export const waitTime = (time = 100) =>
  new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });

// 模拟复制
export const unsecuredCopyToClipboard = (text: string) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  document.execCommand('copy');
  document.body.removeChild(textArea);
};

// 获取父级url
export function getOriginUrl() {
  const userAgent = navigator.userAgent.toLowerCase();
  const isFirefoxOrIE =
    userAgent.indexOf('firefox') > -1 ||
    userAgent.indexOf('msie') > -1 ||
    userAgent.indexOf('trident/') > -1;

  if (isFirefoxOrIE) {
    return location.origin; //eslint-disable-line
  }
  return location.ancestorOrigins.length > 0 //eslint-disable-line
    ? location.ancestorOrigins[0] //eslint-disable-line
    : `https://${location.hostname}`; //eslint-disable-line
}

/**
 * 根据租户 id 组合前台地址
 * @param tenantId 租户 id
 * @param shopId 商品 id
 * @param skuId 商品 skuid
 * @return 前台商品详情地址
 */
export async function tenantId2Url(tenantId: string, shopId: string, skuId: number): Promise<any> {
  try {
    const res = await getDomain({ tenantId, source: 0 });
    const transform = encodeURIComponent(
      `/goods-details?shopId=${shopId}&shopSkuId=${skuId}&tenantId=${tenantId}`
    );
    return `https://${res.domain}/system?mico-app=${transform}`;
  } catch (error) {
    console.warn('[log tenantId2Url error]: ', error);
    message.warn('获取站点域名异常, 请联系管理员');
    return '';
  }
}

/** 随机生成文档的表情和背景图 */
export const randomEmojiBgImg = (): { emoji: string; picture: string } => {
  // 随机取数组中的一个值
  const randomEmojiCatrgory =
    headCoverEmojiJSON[Math.floor(Math.random() * headCoverEmojiJSON.length)];
  const randomEmoji =
    randomEmojiCatrgory.emojis[Math.floor(Math.random() * randomEmojiCatrgory.emojis.length)];

  const randomPictureCatrgory =
    headCoverPicturesJSON[Math.floor(Math.random() * headCoverPicturesJSON.length)];
  const randomPicture =
    randomPictureCatrgory.list[Math.floor(Math.random() * randomPictureCatrgory.list.length)];

  return {
    emoji: randomEmoji.imgUrl,
    picture: randomPicture,
  };
};
