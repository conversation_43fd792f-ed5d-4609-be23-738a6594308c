import { ResultItem } from '@/store/useAiSearchStore';
import { IAppendWldTreeNode, INodeData } from '@/interface/ai-channel-types';
import { v4 as uuid } from 'uuid';

// 历史数据转化成画板节点(由于现在的逻辑是固定的，所以硬编码)
export default function translateToPanelNodes(
  sId: string,
  list: ResultItem[],
  userInfo: {
    nickname: string;
    avatar: string;
  }
) {
  const sessionId = sId;
  const nodes: Array<IAppendWldTreeNode<INodeData>> = [];
  const createTime = list[0].time ? new Date(list[0].time).getTime() : new Date().getTime();
  nodes.push({
    nodeType: 'ask',
    id: list[0].id,
    sessionId,
    isLocation: true,
    loading: false,
    data: {
      content: {
        text: list[0].title || '',
      },
      createTime,
      userInfo,
    },
  });

  list[0].tasks?.forEach((item) => {
    nodes.push({
      nodeType: 'text',
      id: item.id,
      prev: list[0].id,
      sessionId,
      loading: false,
      data: {
        content: {
          text: item.title,
        },
      },
    });
  });

  list[0].tasks?.forEach((item) => {
    let existLink = false;
    if (
      item.answerData &&
      item.answerData.linkObj &&
      item.answerData.linkObj.linkList &&
      item.answerData.linkObj.linkList.length > 0
    ) {
      nodes.push({
        nodeType: 'link',
        id: item.answerData.linkObj.id,
        prev: item.id,
        sessionId,
        nodeHeight: 462,
        loading: true,
        data: {
          content: item.answerData.linkObj.linkList,
          createTime: new Date().getTime(),
        },
      });
      existLink = true;
    }
    if (item.answerData && item.answerData.textObj && item.answerData.textObj.textVal) {
      nodes.push({
        nodeType: 'content',
        id: item.answerData.textObj.id,
        prev: existLink ? item.answerData.linkObj.id : item.id,
        sessionId,
        loading: false,
        data: {
          content: {
            text: item.answerData.textObj.textVal
              .replace(/【\d+†source(?:,\d+(?:-\d+)?)?】/g, '')
              .replace(/\[object Object\]/g, ''),
            images: [], // 是否携带图片数组
          },
          createTime,
        },
      });
    }
  });

  if (list[0].source && list[0].source.length > 0) {
    const goodsList: any[] = [];
    if (list[0].source && list[0].source.length > 0) {
      list[0].source.forEach((item) => {
        if (item.type === 1) {
          goodsList.push({
            id: `${item.id}-${uuid()}`,
            title: item.title,
            desc: item.desc,
            price: item.price,
            imgUrl: item.imgUrl,
            productId: item.productId,
            shopId: item.shopId,
            skuId: item.skuId,
            tenantId: item.tenantId,
            shopName: item.shopName,
            unit: item.unit,
          });
        }
      });
    }
    if (goodsList && goodsList.length > 0) {
      nodes.push({
        nodeType: 'goods',
        id: uuid(),
        prev: list[0].id,
        sessionId,
        nodeHeight: 462,
        loading: true,
        data: {
          content: goodsList,
          createTime,
        },
      });
    }
  }
  return nodes;
}
