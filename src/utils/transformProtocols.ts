import { v4 as uuid } from 'uuid';
import { JSONContent } from '@tiptap/core';
import type { PageBlockDetail, IBlock } from '@/apis';

const textType = ['paragraph', 'heading'];

let maxError = 0;

export const transformBlock = (
  content: string[],
  blocksContent: JSONContent[],
  blockMap: Record<string, IBlock>,
  generateUuid?: boolean
) => {
  if (maxError >= 10) {
    console.error(`错误次数达到10次`);
    return;
  }
  content.forEach((blockId) => {
    if (blockMap[blockId]) {
      const b: JSONContent = {
        type: blockMap[blockId].type,
        attrs: blockMap[blockId].attrs,
      };
      if (!!generateUuid && b.attrs) {
        b.attrs.blockId = uuid();
      }
      if (textType.includes(blockMap[blockId].type)) {
        b.content = blockMap[blockId].attrs.content;
      } else {
        b.content = [];
        transformBlock(blockMap[blockId].content, b.content, blockMap, generateUuid);
      }
      if (!generateUuid && b.attrs && b.attrs.blockId) {
        // 如果是容器模板可能会有问题
        b.attrs.blockId = blockId;
      }
      blocksContent.push(b);
    } else {
      maxError += 1;
      // message.error(`数据异常,未找到block为${blockId}的node`);
      console.error(`数据异常, 未找到block为${blockId}的node`);
    }
  });
};

const transformProtocols = (data: PageBlockDetail, json: JSONContent) => {
  maxError = 0;
  if (data.type === 'page') {
    // eslint-disable-next-line no-param-reassign
    json.type = 'doc';
    // eslint-disable-next-line no-param-reassign
    json.content = [];
    // eslint-disable-next-line no-param-reassign
    json.parentId = data.parentId;
    // eslint-disable-next-line no-param-reassign
    json.spaceId = data.spaceId;
    // eslint-disable-next-line no-param-reassign
    json.attrs = {
      blockId: data.blockId,
    };
    if (data.content && data.content.length > 0) {
      transformBlock(data.content, json.content, data.block);
    } else {
      json.content.push({
        type: 'paragraph',
        attrs: {
          fullWidth: 'false',
          blockId: uuid(),
        },
      });
    }
  }

  return json;
};

export default transformProtocols;
