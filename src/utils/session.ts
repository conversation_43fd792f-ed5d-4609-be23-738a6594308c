export interface UserRelation {
  relation: unknown;
}

/**
 * 会话类型
 */
export const SessionTypes = {
  CHAT: 0, // 私聊
  GROUP: 1, // 群组
  FUNCTION: 2, // 功能
  APP: 3, // 应用
  SHOP: 4, // 店铺
  TOPIC: 5, // 话题
  TENDER: 6, // 招标信息
  CUSTOMER_SERVICE: 7, // 客服会话

  ROBOT_CHAT: 9, // 机器人聊天
  AI_ASSISTANT: 10, // AI 助手
};

/**
 * 用户关系
 */
export const UserRelations = {
  NONE: 0, // 没有关系
  FRIEND: 1, // 朋友
  COLLEAGUE: 2, // 同事
  FRIEND_AND_COLLEAGUE: 3, // 既是同事也是好友
  STRANGER: 4, // 陌生人
};

/**
 * 圈子类型
 */
export const GroupTypes = {
  COMMON: 0, // 普通群
  INNER: 1, // 内部群
  ALL_USER: 2, // 全员群
  DEPARTMENT: 3, // 部门群
  PROJECT: 4, // 项目群
  ANONYMOUS: 5, // 匿名群
  INDUSTRY: 6, // 行业群
  EXHIBITION: 7, // 展会群
  CUSTOMER: 8, // 客户群
  COOPERATION: 9, // 合作群
};

/**
 * 和当前用户是好友
 * @param value
 */
export const isFriend = (value: UserRelation) =>
  value.relation === UserRelations.FRIEND || value.relation === UserRelations.FRIEND_AND_COLLEAGUE;

/**
 * 和当前用户是同事
 * @param value
 */
export const isColleague = (value: UserRelation) =>
  value.relation === UserRelations.COLLEAGUE ||
  value.relation === UserRelations.FRIEND_AND_COLLEAGUE;

/**
 * 和当前用户是陌生人
 * @param value
 */
export const isStranger = (value: UserRelation) => value.relation === UserRelations.STRANGER;

/**
 * 判断是属于公司的群聊
 * @param session
 */
export const isCompanyChatGroup = (session: { groupType: number }) =>
  session.groupType === GroupTypes.INNER ||
  session.groupType === GroupTypes.ALL_USER ||
  session.groupType === GroupTypes.DEPARTMENT;
