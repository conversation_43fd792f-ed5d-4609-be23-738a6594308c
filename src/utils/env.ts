import { getOriginUrl } from './tools';

const getEnv = () => {
  const url = getOriginUrl();
  return url;
};

export default getEnv;

// 根据接口域名判断环境(临时)
export const getEnvWithApiUrl = () => {
  const apiUrl = getEnv();
  if (apiUrl?.includes('localhost')) {
    return 'test';
  }
  if (apiUrl?.includes('test')) {
    return 'test';
  }
  if (apiUrl?.includes('gray')) {
    return 'gray';
  }
  if (apiUrl?.includes('gate.huahuabiz.com')) {
    return 'prod';
  }
};

// 建站前台域名(预览场景)
export const getFrontSiteUrl = () => {
  const nodeEnv = getEnvWithApiUrl();
  if (nodeEnv === 'test') {
    return 'https://doc-test.myhuahua.com';
  }
  if (nodeEnv === 'gray') {
    return 'https://doc-gray.myhuahua.com';
  }
  if (nodeEnv === 'prod') {
    return 'https://doc.huahuabiz.com';
  }
};
// Ai前台域名首页的域名
export const getFrontSiteUrlAi = () => {
  const nodeEnv = getEnvWithApiUrl();
  if (nodeEnv === 'test') {
    return 'https://test.myhuahua.com';
  }
  if (nodeEnv === 'gray') {
    return 'https://gray.myhuahua.com';
  }
  if (nodeEnv === 'prod') {
    return 'https://huahuabiz.com';
  }
  return 'http://localhost:9000/';
};
