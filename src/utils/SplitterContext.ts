import { createContext, MutableRefObject } from 'react';

export type urlObject = {
  url: string;
  type?: string;
};

export type WidthState = number | 'max' | 'none';

export interface SplitterProviderProp {
  isFullRight: boolean; // 是否右侧全屏
  isClosed: null | MutableRefObject<boolean>; // 是否右侧关闭
  LeftWidthBefore: null | MutableRefObject<number>; // 全屏前宽度
  isAutoWidthEnable: null | MutableRefObject<boolean>; // 是否启用自适应宽度
  breakPoint: boolean; // 这个是同步的SiderProvider内容，Splitter内容区域建议用这个，左侧栏区域建议用SiderProvider的breakPoint
  closeRight: () => void;
  fullScreenLeft: () => void;
  fullScreenRight: (foucs?: boolean) => void;
  setRightWidth: (width: WidthState, uobj?: urlObject) => void;
  setAutoWidthEnable: (is?: boolean) => void;
  setLastUrlCache: (cache: string) => void;
}
const SplitterContext = createContext<SplitterProviderProp>({
  LeftWidthBefore: null,
  fullScreenLeft: () => {},
  fullScreenRight: () => {},
  isFullRight: false,
  isClosed: null,
  breakPoint: false,
  isAutoWidthEnable: null,
  closeRight: () => {},
  setRightWidth: () => {},
  setAutoWidthEnable: () => {},
  setLastUrlCache: () => {},
});
export default SplitterContext;
