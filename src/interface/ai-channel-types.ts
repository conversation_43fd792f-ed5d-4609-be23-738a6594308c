export type NodeType =
  | 'root'
  | 'ask'
  | 'content'
  | 'loading'
  | 'waiting'
  | 'text'
  | 'image'
  | 'end'
  | 'summary'
  | 'goods'
  | 'link';

/**
 * 提问的数据类型
 */
export interface IASKData {
  content: {
    text: string;
    images?: string[]; // 是否携带图片数组
  };
  createTime: number;
  userInfo: {
    nickname: string;
    avatar: string;
  };
}

/**
 * 文本数据类型
 */
export interface ITextData {
  content: {
    text: string;
  };
}

/**
 * 文本markdown的数据类型
 */
export interface IContentData {
  content: {
    text: string;
  };
  createTime: number;
  // userInfo: {
  //   nickname: string;
  //   avatar: string;
  // };
}

/**
 * 链接节点
 */
export interface ILinkData {
  content: Array<{
    title: string;
    link: string;
    context: string;
  }>;
  createTime: number;
}

/**
 * 图片节点
 */
export interface IImageData {
  images: string[];
  createTime: number;
}

/**
 * 商品节点
 */
export interface IGoodData {
  content: Array<{
    id: string;
    title: string;
    desc: string;
    price: string;
    imgUrl: string;
  }>;
  createTime: number;
}

/**
 * 节点数据
 */
export type INodeData = IASKData | ITextData | IContentData | ILinkData | IImageData | IGoodData;

/**
 * 追加节点的类型
 */
export interface IAppendWldTreeNode<T> {
  nodeType: NodeType;
  id: string;
  sessionId: string;
  isLocation?: boolean;
  prev?: string;
  nodeHeight?: number;
  loading?: boolean;
  data: T;
}

/**
 * 编辑节点的类型
 */
export interface IUpdateWildTreeNode<T> {
  nodeType: NodeType;
  sessionId: string;
  id: string;
  loading?: boolean;
  nodeHeight?: number;
  data: T;
}

/**
 * 插入节点的类型
 */
export interface IInsterWldTreeNode<T> {
  nodeType: NodeType;
  id: string;
  sessionId: string;
  isLocation?: boolean;
  prev?: string;
  index?: number;
  nodeHeight?: number;
  loading?: boolean;
  data: T;
}
