.pages-box {
  width: 100%;
  height: 100%;
  padding: 0 !important;
  position: relative;
  border-radius: 18px;
  backdrop-filter: none !important;
  background: rgb(255 255 255 / 60%);

  .ech-page-body {
    height: 100%;
    background: transparent !important;
  }
}

.pages-sider {
  width: 290px;
  @base-ani-time: 0.2s;

  overflow: hidden;
  transition: width @base-ani-time ease-in-out, min-width @base-ani-time ease-in-out,
    max-width @base-ani-time ease-in-out;

  &_float {
    height: 100% !important;
    position: absolute;
    top: 0;
    z-index: 10;
    border-radius: 18px;
  }

  .ant-layout-sider-children {
    height: 100%;
  }

  &_close {
    width: 70px;
  }

  &-mobile {
    position: absolute;
    z-index: 20;
    border-radius: 0 8px 8px 0;
  }
}

.pages-mobile {
  &-header {
    display: flex;
    width: 100%;
    padding: 16px 12px 0;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;

    &_right {
      display: flex;
      gap: 5px;
    }

    &_float {
      position: absolute;
      z-index: 1;
    }
  }

  &-btn {
    margin: 0 2px;
    padding: 0 4px;
    transition: background 0.3s linear;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background: rgb(177 179 190 / 20%);
    }
  }

  &_mask {
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: 19;
    background-color: rgb(0 0 0 / 10%);
  }
}

.pages {
  &-wrapper {
    display: flex;
    height: 100%;
    overflow: hidden hidden;
    position: relative;
    transform: translateY(0);
    flex-direction: row;
    background: transparent !important;
  }

  &-float-btn {
    width: 24px;
    height: 24px;
    position: absolute;
    z-index: 8;
    inset: 2% auto auto 2%;
    cursor: pointer;
    user-select: none;
  }

  &-main {
    width: 100%;
    height: 100%;
    border-radius: 0 18px 18px 0;
    position: relative;
    background: transparent;
  }

  &-insdier {
    width: 100%;
  }
}

.backdrop-filter-el {
  position: absolute;
  inset: 0;
  border-radius: 18px;
  background: rgb(255 255 255 / 10%);
  backdrop-filter: blur(30px);
}
