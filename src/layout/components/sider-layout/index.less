.sider {
  &-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 16px;
    position: relative;
    // background-image: url('./images/sider-bg.png');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    // background-position: 0 0;
    border-radius: 18px;
    background: rgb(255 255 255 / 60%);
    backdrop-filter: blur(31px);
    box-shadow: 0 11px 30px 0 rgb(21 72 191 / 50%), inset 0 0 3px 0 #fff;
    gap: 10px;

    &_float {
      backdrop-filter: blur(25px);
    }
  }
  // 小屏
  &-mobile {
    border-radius: 8px;
  }

  &-headTitle {
    display: flex;
    justify-content: space-between;
  }
  // 顶部
  &-logo {
    &_wrapper {
      display: flex;
      align-items: center;
    }

    &_icon {
      width: 24px;
      height: 24px;
      border-radius: 12px;
      margin: 0 8px;
    }

    &_text {
      //
    }
  }
  // 菜单项包裹
  &-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden scroll;
    position: relative;
    flex-direction: column;
    gap: 6px;

    &::-webkit-scrollbar {
      width: 0;
    }

    &_bottom {
      display: flex;
      width: 100%;
      justify-content: center;
      flex-direction: column;
      align-items: flex-start;
    }

    &_userInfo {
      width: 100%;

      &-wrapper {
        display: inline-flex;
        width: 100%;
        align-items: center;

        &-icon {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          vertical-align: middle;
          user-select: none;
          cursor: pointer;
        }

        &-name {
          color: rgb(4 9 25 / 70%);
          font-size: 16px;
          font-weight: bold;
          line-height: 24px;
          margin-left: 4%;
          overflow: hidden;
          text-wrap: nowrap;
          text-overflow: ellipsis;
          letter-spacing: 0;
          user-select: none;
          cursor: pointer;
        }
      }
    }

    &_asideClose {
      display: flex;
      height: 100%;
      padding: 5px 8px;
      transition: background 0.3s linear;
      align-items: center;
      user-select: none;
      cursor: pointer;
      gap: 10px;
      border-radius: 8px;

      &:hover {
        background: rgb(177 179 190 / 20%);
      }

      > i {
        font-size: 18px !important;
      }

      > span {
        color: rgb(4 9 25 / 70%);
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        overflow: hidden;
        user-select: none;
        text-wrap: nowrap;
        text-overflow: ellipsis;
        letter-spacing: 0;
      }
    }

    &_divider {
      margin: 15px 0;
      padding: 0;
    }
  }

  &-float-layout {
    border-radius: 16px;
    box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
  }
}

.sider-new {
  &-session {
    width: 100%;
    height: 38px;
    margin-top: 15px;
    cursor: pointer;

    &:active {
      transform: scale(0.99);
    }
  }

  &-icon {
    display: flex;
    width: 22px;
    height: 20px;
    padding: 2px 4px;
    border-radius: 4px;
    background: rgb(255 255 255 / 20%);
  }
}

.sider-root-container {
  background: transparent;
  width: 100%;
  // 覆盖treenode 原生样式
  .ant-tree-switcher {
    display: none;
    visibility: hidden;
  }

  .ant-tree-list-holder-inner {
    gap: 20px;

    .ant-tree-treenode {
      width: 100%;
      flex: 1;
    }
  }

  .ant-tree-node-content-wrapper {
    width: 100%;
  }
}

.sider-history {
  &_wrapper {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  &_content {
    color: #040919;
    font-size: 14px;
    font-weight: normal;
    width: 100%;
    line-height: 22px;
    padding-bottom: 16px;
    text-align: center;
    letter-spacing: 0;
    user-select: none;
    cursor: pointer;

    &_button {
      padding: 8px;
      border-radius: 8px;
      transition: background 0.3s linear;

      &:hover {
        background: rgb(177 179 190 / 20%);
      }
    }
  }
}
// 隐藏后布局
.sider-hidden {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 20px;

  &_btn {
    display: flex;
    width: 38px;
    height: 38px;
    justify-content: center;
    transition: box-shadow 0.2s linear;
    align-items: center;
    cursor: pointer;
    border-radius: 8px;

    &:hover,
    &_selectItem {
      background: white;
      box-shadow: 0 0 8px 0 #d9dff1;
    }
  }

  &_btnCircle {
    color: white;
    width: 38px;
    height: 38px;
    z-index: 2;
    border-radius: 50%;
    background: linear-gradient(
      327deg,
      rgb(250 162 76 / 80%) -29%,
      rgb(255 91 138 / 80%) 36%,
      rgb(64 58 244 / 80%) 126%
    ) !important;
  }

  &_btnUserInfo {
    background: transparent;
    bottom: 12%;

    &:hover {
      background: transparent;
      box-shadow: none;
    }

    > img {
      width: 38px;
      height: 38px;
      border-radius: 50%;
    }
  }
}

.sider-widget {
  &__rename {
    display: flex;
    display: none;
    min-width: 350px;
    padding: 4px 8px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    flex-direction: column;
    background: #ecf1f4;
    box-shadow: -8px 8px 24px 0 rgb(2 9 58 / 16%);
    border-radius: 10px;

    &.show {
      display: block;
    }

    .rename-input {
      height: 32px !important;
      line-height: 32px !important;
      line-height: 2;
      padding: 0 12px !important;
      border-radius: 6px;
      border: 1px solid #b1b3be;
    }

    .page-icon {
      width: 32px;
      height: 32px;
      margin-right: 4px;
      padding: 6px;
      border-radius: 6px;
      background: #fff;
      border: 1px solid #b1b3be;

      img {
        width: 20px;
        height: 20px;
        vertical-align: top;
      }
    }
  }
}

.sider-menu {
  display: flex;
  padding: 2px 6px;
  flex-direction: column;
  border-radius: 8px;
  gap: 2px;

  &-item {
    display: flex;
    align-items: center;
    border-radius: 8px;
    user-select: none;
    cursor: pointer;
    padding: 3px 8px;
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: background 0.2s linear;
    border: 1px solid transparent;

    &:hover {
      background: rgb(255 255 255 / 50%);
      border: 1px solid rgb(0 140 255 / 50%);
    }

    > i {
      margin: 0 3px;
    }
  }
}

.ant-popover-arrow {
  visibility: hidden;
}

.ant-popover-inner-content {
  padding: 8px;
}

.pop_mask {
  z-index: 99 !important;
}
