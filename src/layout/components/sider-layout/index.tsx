import { useContext, useRef } from 'react';
import classNames from 'classnames';
import { Tooltip, message } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { useNavigate, useLocation, matchPath } from 'react-router-dom';
import SiderProvider from '@/utils/SiderProvider';
import { usePanelStore, useAgent, useAiSearchStore } from '@/store';
import { resIcon } from './consts';
import SiderItem from './components/sider-item';
import './index.less';

function SiderLayout() {
  const location = useLocation();
  const navigate = useNavigate();
  const {
    onCollapsed,
    Collapsed,
    CanFloatSider,
    FloatMouseEnterEvent,
    FloatMouseLeaveEvent,
    breakPoint,
  } = useContext(SiderProvider);
  const { setPanelVisiable } = usePanelStore();
  const { avatar, name, typeCode } = useAgent();
  const { resultData, resetCurrentAskInfo } = useAiSearchStore();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const SiderItemList = useRef<any[]>([
    {
      id: '004',
      name: '最近交流',
      key: '004',
      icon: resIcon,
      children: [],
      onTap: () => {
        setPanelVisiable(false);
      },
    },
  ]);

  return (
    <div
      className={classNames('sider-box', {
        'sider-hidden': Collapsed,
        'sider-mobile': breakPoint,
        'sider-box_float': CanFloatSider,
        'sider-float-layout': CanFloatSider,
      })}
      onMouseEnter={() => CanFloatSider && FloatMouseEnterEvent()}
      onMouseLeave={() => CanFloatSider && FloatMouseLeaveEvent()}
    >
      <div className="sider-headTitle">
        <div className="sider-logo_wrapper">
          <img className="sider-logo_icon" src={avatar} alt="" />
          <div className="sider-logo_text">{name}</div>
        </div>
        <div role="button" tabIndex={-1} className="sider-wrapper_asideClose" onClick={onCollapsed}>
          <Tooltip title="收起" placement="right">
            <Icon name="drawing_board" size={18} />
          </Tooltip>
        </div>
      </div>
      <div
        className="sider-new-session"
        onClick={(e) => {
          e.stopPropagation();
          resetCurrentAskInfo();

          if (typeCode === 'customAgent') {
            if (matchPath('/:agentId/new-chat', location.pathname) && resultData.length === 0) {
              message.warning('当前已经在新建会话页面');
            } else {
              navigate('new-chat', { replace: true });
            }
            return;
          }

          if (matchPath('/:agentId/home', location.pathname)) {
            message.warning('当前已经在主页');
          } else {
            navigate('home', { replace: true });
          }
        }}
      >
        <img src="https://img.huahuabiz.com/user_files/20241227/1735285791479734.png" alt="" />
      </div>
      <div className="sider-wrapper">
        {SiderItemList.current.map((item) => (
          <SiderItem key={item.key} nodeData={item} />
        ))}
      </div>
    </div>
  );
}
export default SiderLayout;
