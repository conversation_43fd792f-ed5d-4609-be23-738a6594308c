import { CreateDocPageChildParams } from '@/apis/doc-page/create-doc-page-child';
import { CreateDocPageFirstParams } from '@/apis/doc-page/create-doc-page-first';

export interface DataItem {
  key: string;
  title: string;
  children?: DataItem[];
  // eslint-disable-next-line no-unused-vars
  onTap?: (_node_data: DataItem) => void;
}
export interface PermSpaceListData {
  id: string;
  spaceId: string;
  name: string;
  logo: string;
  description: string;
  roleIds: string[];
  type: string;
  key: string;
  blockId: string;
  children: PermSpaceListData[];
  spaceName: string;
  spaceLogo: string;
  pageList: PermSpaceListData[];
  content: string[];
  ownerId: number;
  hasChild: boolean;
  parentId: string;
  perCodes: string[];
  isOwner: boolean;
  attrs: {
    pageName: string;
    logo: string;
  };
  /** 空间类型 1成员私有空间  2团队空间  3模板空间 */
  spaceType: number;
  // eslint-disable-next-line no-unused-vars
  onTap?: () => void;
}

export type ParamsObject = { blockId: string } & { spaceId: string };
export type ParamsCreateObject = CreateDocPageFirstParams & CreateDocPageChildParams;
