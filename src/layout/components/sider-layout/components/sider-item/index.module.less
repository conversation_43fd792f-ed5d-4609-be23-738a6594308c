.SiderItemBox {
  width: 100%;
  border-radius: 8px;
  opacity: 1;
  display: flex;
  flex-direction: column;
  user-select: none;
  cursor: pointer;
  position: relative;

  z-index: 2;
  .siderWrapper {
    display: flex;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;

    transition: background 0.3s linear;
    border: 1px solid transparent;
    &:hover {
      background: rgba(255, 255, 255, 0.5);
      border: 1px solid rgba(0, 140, 255, 0.5);
    }

    .siderIcon {
      height: 24px;
      margin: 0 8px;
      font-weight: bold;
      display: flex;
      align-items: center;
      > img {
        min-width: 16px;
        min-height: 16px;
        width: 16px;
        height: 16px;
        object-fit: contain;
      }
    }
    .siderText {
      width: 100%;
      height: 100%;
      font-size: 16px;
      line-height: 24px;
      font-weight: bold;
      letter-spacing: 0em;
      color: rgba(4, 9, 25, 0.7);
    }

    .operateContainer {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;
      margin-right: 6px;
      .operateBtn {
        width: 20px;
        border-radius: 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background 0.3s linear;
        &:hover {
          background: rgba(177, 179, 190, 0.2);
        }
      }
    }
  }

  .childrenWrapper {
    width: 100%;
    overflow: hidden;
    position: relative;
    padding: 0 0 0 5px;
    box-sizing: border-box;
    display: grid;
    grid-template-rows: 0fr;

    transition: grid 0.2s linear;
    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 100%;
      margin-top: 5px;
      margin-left: 15px;
      background: rgba(4, 9, 25, 0.1);
      position: absolute;
      top: 0;
    }
  }

  .bottomContainer {
    width: 100%;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    user-select: none;
    gap: 10px;
    .bottomWrapper {
      height: 100%;

      cursor: pointer;
      padding: 5px 8px;
      border-radius: 8px;
      transition: background 0.3s linear;
      &:hover {
        background: rgba(177, 179, 190, 0.2);
      }
    }
  }

  .rootContainer {
    display: block;
    padding: 7px 0;
    background: transparent;

    // 覆盖 treenode 原生样式
    :global {
      .ant-tree-list-holder {
        position: relative;
        &::after {
          content: '';
          top: 0;
          left: 6%;
          width: 1px;
          height: 100%;
          position: absolute;
          background: rgba(4, 9, 25, 0.1);
        }
      }
      .ant-tree-switcher {
        display: none;
        visibility: hidden;
      }
      .ant-tree-list-holder-inner {
        margin-left: 20px;
        .ant-tree-treenode {
          width: 100%;
          flex: 1;
        }
      }
      .ant-tree-node-content-wrapper {
        width: 226px;
      }
    }
  }
}
