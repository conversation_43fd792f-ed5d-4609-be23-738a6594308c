import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { useAgent, useAiSearchStore } from '@/store';
import { findIcon, homeIcon, resIcon, spaceIcon } from '../../consts';
import { PermSpaceListData } from '../../types';
import SiderTreeBase from '../sider-tree-base';
import style from './index.module.less';

interface SiderItemProp {
  nodeData: PermSpaceListData;
}

function SiderItem({ nodeData }: SiderItemProp) {
  const { typeId, typeCode } = useAgent();
  const { getChatHistory } = useAiSearchStore();
  const [show, setShow] = useState(true);
  const childrenWrapperRef = useRef<HTMLDivElement | null>(null);

  const isList = useMemo(() => ['空间', '最近交流'].includes(nodeData.name), [nodeData.name]);

  const handleTap = () => {
    if (isList) {
      setShow(!show);
    }
    nodeData.onTap?.();
  };

  useEffect(() => {
    if (typeCode && typeId) {
      getChatHistory();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [typeId, typeCode]);

  return (
    <div role="button" tabIndex={-1} className={style.SiderItemBox}>
      <div
        className={style.siderWrapper}
        onClick={(e) => {
          e.stopPropagation();
          handleTap();
        }}
      >
        <span className={style.siderIcon}>
          {nodeData.key === '001' && <img src={homeIcon} alt="" />}
          {nodeData.key === '002' && <img src={findIcon} alt="" />}
          {nodeData.key === '003' && <img src={spaceIcon} alt="" />}
          {nodeData.key === '004' && <img src={resIcon} alt="" />}
        </span>
        <span className={style.siderText}>{nodeData.name}</span>
      </div>

      {isList && (
        <div
          key={nodeData.key}
          ref={childrenWrapperRef}
          role="button"
          tabIndex={-1}
          className={style.childrenWrapper}
          style={{ gridTemplateRows: show ? `1fr` : '0fr' }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {nodeData.name === '最近交流' && <SiderTreeBase />}
        </div>
      )}
    </div>
  );
}
SiderItem.defaultProp = {
  title: '',
  nodeData: {},
};
export default memo(SiderItem);
