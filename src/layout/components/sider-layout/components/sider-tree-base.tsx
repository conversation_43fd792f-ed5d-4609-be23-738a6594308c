import { useRef, useState } from 'react';
import classNames from 'classnames';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { HistoryListItemResType } from '@/apis/ech-ai-py/get-wldtree-history-list';
import { Tooltip } from '@echronos/antd';
import { useAiSearchStore } from '@/store';
import style from './sider-tree-base.module.less';

interface ContextProp {
  data: HistoryListItemResType;
  isSelected: string;
  // eslint-disable-next-line no-unused-vars
  onClick: (data: HistoryListItemResType) => void;
}

function Content({ data, onClick, isSelected }: ContextProp) {
  const parentRef = useRef<HTMLDivElement>(null);
  const locations = useLocation();
  const lastUrl = locations.pathname.substring(locations.pathname.lastIndexOf('/') + 1);
  return (
    <div
      ref={parentRef}
      className={classNames(style.siderTreeBaseBox, {
        [style.siderTreeBaseSelect]: isSelected === data.scene_id || lastUrl === data.scene_id,
      })}
      onClick={() => onClick(data)}
    >
      <Tooltip title={data.collapsed[data.collapsed.length - 1].prompt} placement="right">
        <div className={style.siderTreeBaseText}>
          {data.collapsed[data.collapsed.length - 1].prompt}
        </div>
      </Tooltip>
    </div>
  );
}

function SiderTreeBase() {
  const { chatData } = useAiSearchStore();
  const navigate = useNavigate();
  const params = useParams();
  const [select, setSelect] = useState('');

  const handleClick = (data: HistoryListItemResType) => {
    if (params.session_id === data.scene_id) {
      return;
    }
    setSelect(data.scene_id);
    navigate(`chat/${data.scene_id}`);
  };

  return (
    <div className={style.siderBaseContainer}>
      {chatData.chatList.map((v) => (
        // @ts-expect-error todo
        <Content key={v.scene_id} data={v} isSelected={select} onClick={handleClick} />
      ))}
    </div>
  );
}
export default SiderTreeBase;
