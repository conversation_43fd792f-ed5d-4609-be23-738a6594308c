.siderBaseContainer {
  width: 100%;
  border-radius: 8px;
  position: relative;
  margin-left: 25px;
  min-height: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  text-wrap: nowrap;

  .siderTreeBaseBox {
    width: 90%;
    border-radius: 8px;
    padding: 0 12px 0 8px;
    margin: 2px 0;
    transition: background 0.3s linear;
    height: 24px;
    line-height: 24px;

    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
    border: 1px solid transparent;

    .siderTreeBaseText {
      height: 100%;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;

      font-size: 12px;
      font-weight: normal;
      letter-spacing: 0em;
      color: rgba(4, 9, 25, 0.7);
    }

    &:hover {
      background: rgba(255, 255, 255, 0.5);
      border: 1px solid rgba(0, 140, 255, 0.5);
    }
  }
  .siderTreeBaseSelect {
    position: relative;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(0, 140, 255, 0.5);
    &::after {
      content: '';
      display: block;
      width: 2px;
      background-color: #008cff;
      position: absolute;
      top: 11px;
      bottom: 11px;
      left: 0;
    }
  }
}
