import { useNavigate, useLocation, matchPath } from 'react-router-dom';
import { message, Tooltip } from 'antd';
import Icon from '@echronos/echos-icon';
import { useAgent, useAiSearchStore } from '@/store';
import styles from './index.module.less';

interface LoyoutHeadPropsType {
  collapsed: boolean;
  onOpenSider: () => void;
}

function LoyoutHead({ collapsed, onOpenSider }: LoyoutHeadPropsType) {
  const location = useLocation();
  const navigate = useNavigate();
  const { name, typeCode } = useAgent();
  const { resultData, resetCurrentAskInfo } = useAiSearchStore();

  const onCreateSession = () => {
    resetCurrentAskInfo();

    if (typeCode === 'customAgent') {
      if (matchPath('/:agentId/new-chat', location.pathname) && resultData.length === 0) {
        message.warning('当前已经在新建会话页面');
      } else {
        navigate('new-chat', { replace: true });
      }
      return;
    }

    if (matchPath('/:agentId/home', location.pathname)) {
      message.warning('当前已经在主页');
    } else {
      navigate('home', { replace: true });
    }
  };

  return (
    <div className={styles.loyoutHead}>
      {collapsed && (
        <Tooltip title="展开侧边栏" placement="bottom">
          <Icon
            className={styles.openIcon}
            name="drawing_board"
            size={18}
            onClick={(e) => {
              e.stopPropagation();
              onOpenSider();
            }}
          />
        </Tooltip>
      )}
      <Tooltip title="新建会话" placement="bottom">
        <Icon
          className={styles.createIcon}
          name="new_conver_line"
          size={18}
          onClick={onCreateSession}
        />
      </Tooltip>
      <div className={styles.drive} />
      {name}
    </div>
  );
}

export default LoyoutHead;
