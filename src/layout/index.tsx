import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Outlet,
  useSearchParams,
  useParams,
  useNavigate,
  useLocation,
  matchPath,
} from 'react-router-dom';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { Layout } from '@echronos/antd';
import Sider from '@echronos/antd/lib/layout/Sider';
import { createOrUpdateFunctionSession, getDocPageListFirst } from '@/apis';
import SiderProvider from '@/utils/SiderProvider';
import useAgent from '@/store/useAgent';
import SiderLayout from './components/sider-layout';
import LoyoutHead from './components/layout-head';
import styles from './index.module.less';
import './index.less';

function LayoutHome() {
  const { agentId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const aiImPageRef = useRef<HTMLDivElement | null>(null);
  const siderRef = useRef<HTMLDivElement | null>(null);
  const [collapsed, setCollapsed] = useState(false);
  const [aiImPageWidth, setAiImPageWidth] = useState(0);
  const [visible, setVisible] = useState(false);
  const isAutoRetract = useRef(true);
  const { fetchAgent, typeId } = useAgent();

  const [searchParams] = useSearchParams();
  const documentType = searchParams.get('type');
  const spaceId = searchParams.get('spaceId') || '';
  const docId = searchParams.get('docId');

  const SiderInject = useMemo(
    () => ({
      onCollapsed: () => {
        setCollapsed((v) => !v);
      },
      Collapsed: collapsed,
      getSiderWidth: () => siderRef.current?.getBoundingClientRect().width ?? 0,
      breakPoint: false,
      CanFloatSider: false,
      setHoverElem: false,
      isMovingCollapsed: false,
      FloatMouseEnterEvent: () => {},
      FloatMouseLeaveEvent: () => {},
    }),
    [collapsed]
  );

  // 打开侧边栏
  const onOpenSider = () => {
    setCollapsed(false);
  };

  useEffect(() => {
    const aiImPageEl = aiImPageRef.current;

    if (aiImPageEl) {
      const resizeObserver = new ResizeObserver(
        debounce((entries) => {
          // eslint-disable-next-line no-restricted-syntax
          for (const entry of entries) {
            setAiImPageWidth(entry.contentRect.width);
            if (entry.contentRect.width < 700 && !collapsed && isAutoRetract.current) {
              setCollapsed(true);
              isAutoRetract.current = false;
            }
          }
        }, 500)
      );

      resizeObserver.observe(aiImPageEl);

      // 清理函数，组件卸载时调用
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [aiImPageWidth]); // eslint-disable-line

  useEffect(() => {
    createOrUpdateFunctionSession('wldtree');
  }, []);

  useEffect(() => {
    if (documentType && documentType === 'space' && spaceId) {
      getDocPageListFirst({ spaceId }).then((res) => {
        (window.microApp?.getData() as any)?.openDocInDrawer({
          blockId: res.list[0].blockId,
        });
      });
      return;
    }
    if (documentType && documentType === 'doc' && docId) {
      (window.microApp?.getData() as any)?.openDocInDrawer({
        blockId: docId,
      });
    }
  }, [documentType, spaceId, docId]);

  useEffect(() => {
    // 根据传递过来的类型获取智能体信息
    if ((agentId && +agentId !== typeId) || (agentId && !typeId)) {
      fetchAgent(+agentId).then((res: any) => {
        setVisible(true);
        /* 1.已经在某一个会话中, 不跳转 */
        if (matchPath('/:agentId/chat/:id', location.pathname)) {
          return;
        }

        /* 2.没有在任意会话 */
        // 2.1如果会话列表不为空, 跳转到会话列表的第一个
        if (res.sceneId) {
          navigate(`chat/${res.sceneId}`);
          return;
        }

        // 2.2会话列表为空
        // 2.2.1如果是自定义智能体跳转到新建会话
        if (res.code === 'customAgent' || res.code === '') {
          navigate('new-chat');
          return;
        }

        // 2.2.2如果是预制智能体跳转到首页
        navigate('home');
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [agentId]);

  return (
    <div
      ref={aiImPageRef}
      className={styles.page}
      onClick={() => {
        setCollapsed(true);
      }}
    >
      <div className={styles.backdropPage} />
      <div className={styles.layoutHomePage}>
        <Layout>
          <SiderProvider.Provider value={SiderInject}>
            <LoyoutHead collapsed={collapsed} onOpenSider={onOpenSider} />
            <div
              className={classNames(
                styles.qutlet,
                !collapsed && aiImPageWidth > 700 && styles.qutletWidth
              )}
            >
              {visible && <Outlet />}
            </div>
            <Sider
              ref={siderRef}
              trigger={null}
              collapsible
              theme="light"
              width="290px"
              collapsed={collapsed}
              breakpoint="xs"
              collapsedWidth="0"
              style={{
                maxWidth: '290px',
                height: '100%',
                // display: !collapsed ? ' block' : 'none',
              }}
              className={classNames('pages-sider', {
                'pages-sider_close': collapsed,
                'pages-sider_float': true,
              })}
            >
              <SiderLayout />
            </Sider>
          </SiderProvider.Provider>
        </Layout>
      </div>
    </div>
  );
}

export default LayoutHome;
