.page {
  width: 100%;
  height: 100%;
}

.layoutHomePage {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 18px;
  background: rgb(255 255 255 / 50%);

  :global {
    .ant-layout {
      display: flex;
      height: 100%;
      overflow: hidden;
      flex-direction: column;
      background: hsla(0, 0%, 100%, 0);
      border-radius: 18px;
    }
  }
}

.backdropPage {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 18px;
  background: rgb(255 255 255 / 30%);
  backdrop-filter: blur(30px);
}

.qutlet {
  display: flex;
  width: 100%;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}

.qutletWidth {
  width: calc(100% - 290px);
  transform: translateX(290px);
}
