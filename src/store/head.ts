import type { DocumentHeadCoverData } from '@echronos/millet-ui';
import autoUpdateTitle from '@/utils/auto-update-title';
import { create } from 'zustand';

interface DocumentHeadStoreProp {
  headData: DocumentHeadCoverData;
  setHeadData: (val: DocumentHeadCoverData) => void;
}

// 文档编辑头部数据

const useHeadStore = create<DocumentHeadStoreProp>((set) => ({
  headData: {
    avatar: {
      type: 'icon',
      url: '',
    },
    background: {
      url: '',
      transform: 40,
    },
    title: '',
  },
  setHeadData(val) {
    set(() => ({ headData: val }));
    autoUpdateTitle(val.title);
  },
}));

export default useHeadStore;
