/* eslint-disable no-unused-vars */

import { create } from 'zustand';
import { PermSpaceListData } from '@/apis';

// 空间信息接口
export interface SpaceInfoData {
  name: string;
  logo: string;
  description: string;
  blockId: string;
  spaceId: string;
  content: string[];
  ownerId: number;
}

// 页面信息接口
export interface PageInfoData {
  perCodes: string[];
  attrs: {
    pageName: string;
  };
  type: string;
  blockId: string;
  spaceId: string;
}

// Zustand 状态接口
export interface SpaceStore {
  spaceInfo: SpaceInfoData;
  pageInfo: PageInfoData;
  spaceApplyNum: number;
  headerNavList: PermSpaceListData[];
  spaceGroupList: PermSpaceListData[];
  shareGroupList: PermSpaceListData[];
  spaceShareList: PermSpaceListData[];

  // 方法
  setSpaceInfo: (val: SpaceInfoData) => void;
  setPageInfo: (val: PageInfoData) => void;
  setSpaceApplyNum: (val: number) => void;
  setHeaderNavList: (val: PermSpaceListData[]) => void;
  setSpaceGroupList: (val: PermSpaceListData[]) => void;
  setShareGroupList: (val: PermSpaceListData[]) => void;
  setSpaceShareList: (val: PermSpaceListData[]) => void;

  // 初始化本地存储
  initStorage: () => void;
}

// 创建 Zustand Store
const useSpaceStore = create<SpaceStore>((set) => ({
  // 初始化状态
  spaceInfo: {} as SpaceInfoData,
  pageInfo: {} as PageInfoData,
  spaceApplyNum: 0,
  headerNavList: [] as PermSpaceListData[],
  spaceGroupList: [] as PermSpaceListData[],
  shareGroupList: [] as PermSpaceListData[],
  spaceShareList: [] as PermSpaceListData[],

  // 方法实现
  setSpaceInfo: (val) => set({ spaceInfo: val }),
  setPageInfo: (val) => set({ pageInfo: val }),
  setSpaceApplyNum: (val) => set({ spaceApplyNum: val }),
  setHeaderNavList: (val) => set({ headerNavList: val }),
  setSpaceGroupList: (val) => set({ spaceGroupList: val }),
  setShareGroupList: (val) => set({ shareGroupList: val }),
  setSpaceShareList: (val) => set({ spaceShareList: val }),

  // 初始化存储
  initStorage: () => {
    const groupData = localStorage.getItem('GROUP_LIST');
    const shareGroupData = localStorage.getItem('SHARE_GROUP_LIST');
    const shareData = localStorage.getItem('SHARE_LIST');

    if (groupData && JSON.parse(groupData).length) {
      set({ spaceGroupList: JSON.parse(groupData) });
    }
    if (shareGroupData && JSON.parse(shareGroupData).length) {
      set({ shareGroupList: JSON.parse(shareGroupData) });
    }
    if (shareData && JSON.parse(shareData).length) {
      set({ spaceShareList: JSON.parse(shareData) });
    }
  },
}));

export default useSpaceStore;
