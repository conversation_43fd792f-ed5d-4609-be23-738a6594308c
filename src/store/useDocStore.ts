import { create } from 'zustand';
import { IndexeddbPersistence } from 'y-indexeddb';
import type { DocumentHeadCoverData } from '@echronos/millet-ui';
import WebsocketProvider from '@echronos/editor/dist/pkg-y-websocket';

export interface User {
  /** 用户 id */
  id: number;
  /** 用户昵称 */
  nickname: string;
  /** 用户头像 */
  avatar: string;
  /** 用户是否激活 */
  active: boolean;
}

interface State {
  /** 文档加载状态 */
  docLoading: boolean;
  /** 在线用户 */
  onlineUsers: User[];
  /** socket 实例  */
  socketProvider: WebsocketProvider | null;
  /** indexDB 实例  */
  indexDBProvider: IndexeddbPersistence | null;
  /** 文档头部 */
  docHeadInfo: DocumentHeadCoverData;
  /** 预览文档 */
  viewDoc: boolean;
  /** 在搜索页面预览文档 */
  inSearchViewDoc: boolean;
  /** 搜索页面预览文档 id */
  searchViewDocBlockId: string;
  /** 控制发布笔记的显示与隐藏 */
  isShowPublish: boolean;
  /** 文档 id */
  docBlockId: string;
  /** 文档 spaceId */
  docSpaceId: string;
}

type UpdateDocHeadInfoParams = {
  [K in keyof DocumentHeadCoverData]?: DocumentHeadCoverData[K]; // K 为 DocumentHeadCoverData 的键，值为对应的类型
};

interface Actions {
  /** 更新文档加载状态 */
  setDocLoading: (bol: boolean) => void;
  /** 更新在线用户 */
  setOnlineUsers: (user: User[]) => void;
  /** 更新 socket 实例 */
  setIndexDBProvider: (ins: IndexeddbPersistence | null) => void;
  /** 更新 indexDB 实例  */
  setSocketProvider: (ins: WebsocketProvider | null) => void;
  /** 更新文档头部 */
  updateDocHeadInfo: (params: UpdateDocHeadInfoParams) => void;
  /** 更新预览文档 */
  updateViewDoc: (bol: boolean) => void;
  /** 更新在搜索页面预览文档 */
  updateInSearchViewDoc: (bol: boolean) => void;
  /** 更新搜索页面预览文档 id */
  updateSearchViewDocBlockId: (id: string) => void;
  /** 更新点击发布按钮显示发布笔记 */
  updateIsShowPublish: (bol: boolean) => void;
  /** 更新文档 id */
  updateDocBlockId: (id: string) => void;
  /** 更新文档 spaceId */
  updateDocSpaceId: (id: string) => void;
}

const useDocStore = create<State & Actions>((set) => ({
  docLoading: true,
  onlineUsers: [],
  indexDBProvider: null,
  socketProvider: null,
  docHeadInfo: {
    title: '',
    avatar: {
      type: 'icon',
      url: '',
    },
    background: {
      url: '',
      transform: 40,
    },
  },
  viewDoc: false,
  inSearchViewDoc: false,
  searchViewDocBlockId: '',
  /** 控制发布笔记的显示与隐藏 */
  isShowPublish: false,
  docBlockId: '',
  docSpaceId: '',
  setDocLoading(bol) {
    set(() => ({ docLoading: bol }));
  },
  setOnlineUsers(users) {
    set(() => ({ onlineUsers: users }));
  },
  setIndexDBProvider(ins) {
    set(() => ({ indexDBProvider: ins }));
  },
  setSocketProvider(ins) {
    set(() => ({ socketProvider: ins }));
  },
  updateDocHeadInfo(params) {
    set((state) => ({ docHeadInfo: { ...state.docHeadInfo, ...params } }));
  },
  updateViewDoc(bol) {
    set(() => ({ viewDoc: bol }));
  },
  updateInSearchViewDoc(bol) {
    set(() => ({ inSearchViewDoc: bol }));
  },
  updateSearchViewDocBlockId(id) {
    set(() => ({ searchViewDocBlockId: id }));
  },
  updateIsShowPublish(bol) {
    set(() => ({ isShowPublish: bol }));
  },
  updateDocBlockId(id) {
    set(() => ({ docBlockId: id }));
  },
  updateDocSpaceId(id) {
    set(() => ({ docSpaceId: id }));
  },
}));

export default useDocStore;
