/* eslint-disable no-unused-vars */
import { create } from 'zustand';
import { UserType, ShopType, CompanyType } from '@echronos/core';

export interface IUser {
  user: UserType;
  shop: ShopType;
  company: CompanyType;
}

// 使用 Zustand 创建 store
export interface UserStore {
  user: IUser;
  setUser: (user: IUser) => void;
}

// 创建 Zustand store
const useUserInfoStore = create<UserStore>((set) => ({
  user: {} as IUser,
  setUser: (user) => set({ user }),
}));

export default useUserInfoStore;
