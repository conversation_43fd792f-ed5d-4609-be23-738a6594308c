import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export const Explorer = {
  id: 'explorer',
  label: '探索者',
  avatar: 'https://img.huahuabiz.com/user_files/2024125/1733381213825840.png',
} as const;
export const Goods = {
  id: 'goods',
  label: '智汇选',
  avatar: 'https://img.huahuabiz.com/user_files/2024125/1733381213822238.png',
} as const;
export const None = {
  id: 'none',
  label: 'WLDtree AI',
  avatar: 'https://img.huahuabiz.com/user_files/2025317/1742193687292789.png',
} as const;
export const Deepseek = {
  id: 'deepseek',
  label: 'DeepSeek',
  avatar: 'https://img.huahuabiz.com/user_files/2025219/1739957369409795.png',
} as const;

export const intelligenceMap = {
  explorer: Explorer,
  goods: Goods,
  none: None,
  deepseek: Deepseek,
};

export type Intelligence =
  | typeof Explorer.id
  | typeof Goods.id
  | typeof None.id
  | typeof Deepseek.id;

// 定义类型接口
interface CurrentVersion {
  /** 选择版本：s-标准, p -por */
  version: 's' | 'p' | 'u';
  /**  a探索者  b智汇选  z无智能体，初始化为a */
  intelligence: Intelligence;
  /** 控制弹出框显影 */
  display: boolean;
  /** 是否开启pro,开启为true */
  pro: boolean;
  /** 是否开启 deepseek */
  isDeepSeek: boolean;
}

// 定义回调函数接口
interface Actions {
  updateVersion: (version: CurrentVersion['version']) => void;
  updateIntelligence: (intelligence: CurrentVersion['intelligence']) => void;
  updatePro: (pro: CurrentVersion['pro']) => void;
  updateIsDeepSeek: (pro: CurrentVersion['isDeepSeek']) => void;
}

// 初始化数据
const initialState: CurrentVersion = {
  version: 's',
  display: true,
  intelligence: 'none',
  pro: false,
  isDeepSeek: false,
};

const versionStore = create<CurrentVersion & Actions>()(
  persist(
    (set) => ({
      ...initialState,
      updateVersion: (version) => set(() => ({ version })),
      updatePro: (pro) => set(() => ({ pro })),
      updateIntelligence: (intelligence) => set(() => ({ intelligence })),
      updateIsDeepSeek: (isDeepSeek) => set(() => ({ isDeepSeek })),
    }),
    {
      // 存储的名字
      name: 'versionStore',
      // 存储位置
      getStorage: () => localStorage,
    }
  )
);

export default versionStore;
