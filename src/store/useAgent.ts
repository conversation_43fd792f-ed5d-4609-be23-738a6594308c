import { create } from 'zustand';
import { message } from '@echronos/antd';
import { GetAgentsListApiCenter, getChatHistoryList, getAgentDetail } from '@/apis';
import useAiSearchStore from './useAiSearchStore';

interface State {
  /** 智能体 id */
  id: string;
  /** 能体名称 */
  name: string;
  /** 智能体头像 */
  avatar: string;
  /** 智能体 id */
  typeId: number;
  /** 智能体 code */
  typeCode: 'document' | 'customAgent' | 'article' | 'note' | 'head' | 'mail' | '';
  /** 智能体会话 id */
  sessionId: string;
  /** 智能体id */
  ragflowId: string;
  /** 智能体欢迎语 */
  prologue: string;
  /** 智能体提示 */
  prologueQuestions: string[];
}

const initState: State = {
  id: '',
  name: '',
  avatar: 'https://img.huahuabiz.com/default/image/default-avatar.png',
  typeId: 0,
  typeCode: '',
  sessionId: '',
  ragflowId: '',
  prologue: '',
  prologueQuestions: [],
};

interface Actions {
  /** 更新智能体 */
  setAgent: (params: any) => void;
  /** 重置智能体数据 */
  resetAgent: () => void;
  /** 获取对应 code 智能体 */
  fetchAgent: (agentId: number) => Promise<any>;
}

const useAgent = create<State & Actions>((set) => ({
  ...initState,
  setAgent: (params) => {
    set(() => params);
  },
  resetAgent: () => {
    set(() => initState);
  },
  fetchAgent: async (agentId) => {
    const { resetCurrentAskInfo, updateChatData } = useAiSearchStore.getState();
    updateChatData({ chatList: [], chatPageSize: 1, chatTotal: 0 });
    resetCurrentAskInfo();

    try {
      let _typeCode = '';
      // 轻墨文档
      if (agentId === 419775030) {
        set({
          id: '',
          name: '轻墨文档',
          avatar: 'https://img.huahuabiz.com/user_files/2025327/1743045272741605.png',
          typeId: 419775030,
          typeCode: 'document',
        });

        _typeCode = 'document';
      } else if ([415904577, 415904987, 415905134, 415904657].includes(agentId)) {
        const res = await GetAgentsListApiCenter({});
        // 匹配对应智能体
        const currentAgent = res?.list?.find((item: any) => item.user_id === agentId);
        if (currentAgent) {
          set({
            id: currentAgent.id,
            name: currentAgent.name,
            avatar: currentAgent.avatar,
            typeId: agentId,
            typeCode: currentAgent.biz_type,
          });

          _typeCode = currentAgent.biz_type;
        }
      } else {
        // 查询自定义智能体
        const res3 = await getAgentDetail({ id: agentId });
        // 匹配对应智能体
        const currentAgent2 = res3;
        if (currentAgent2) {
          set({
            id: currentAgent2.id,
            name: currentAgent2.name,
            avatar: currentAgent2.avatar,
            typeId: agentId,
            typeCode: 'customAgent',
            ragflowId: currentAgent2.id,
          });

          _typeCode = 'customAgent';
        }
      }

      // 没有智能体类型, 判断被删除
      if (!_typeCode) {
        message.warning('智能体已被删除');
        return { code: '' };
      }
      // 请求智能体聊天记录
      const res2 = await getChatHistoryList({
        virtual_user_id: _typeCode === 'customAgent' ? `${agentId}` : undefined,
        agentRequestType: _typeCode === 'customAgent' ? undefined : _typeCode,
        page_size: 50,
        page_num: 1,
      });

      // 返回第一个会话
      if (res2.items.length) {
        return { sceneId: res2.items[0].scene_id };
      }

      return { code: _typeCode };
    } catch (error) {
      console.log('[log fetchAgent]: ', error);
      message.warning('获取智能体信息异常');
      throw error;
    }
  },
}));

export default useAgent;
