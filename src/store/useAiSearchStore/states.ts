import type { Intelligence } from '@/store/useVersion';
import { TaskListType } from '@/pages/chat/data-type';
import { ChatHistoryListItemResType } from '@/apis/ech-ai-py/get-chat-history-list';

export interface TaskListTypeExt extends TaskListType {
  /** 子任务结果内容 */
  content: string;
  /** 子任务请求控制器 */
  controller: AbortController | null;
}

/** 当前提问信息 */
export interface CurrentAskInfo {
  /** 提问 id */
  id: string;
  /** 当前提问父节点 id */
  parentId?: number;
  /** 当前提问场景 id */
  sceneId?: string;
  /** 当前提问会话 id */
  conversationId?: string;
  /** 请求生成中 */
  fetchGenerating: boolean;
  /** 请求控制器 */
  fetchController: AbortController | null;
  /** 子任务 id */
  refIds?: string[];
}

/** 来源数据(商品) */
export interface SourceType {
  /** 商品 id */
  id: string;
  /** 商品名称 */
  name: string;
  /** 商品 sku */
  skuSearch: string;
  /** 商品价格 */
  marketPrice: string;
  /** 商品图片 */
  images: string;
  /** skuId */
  skuId: number;
  /** shopId */
  shopId: number;
  /** tenantId */
  tenantId: string;
  /** 链接规格 */
  unit: string;
  /** 链接类型 */
  type: 0 | 1;
  /** 链接标题 */
  title?: string;
  /** 链接描述 */
  desc?: string;
  /** 链接商品价格 */
  price?: string;
  /** 链接图片 */
  imgUrl?: string;
  /** 链接商品 id */
  productId?: string;
  /** 链接商品名称 */
  shopName?: string;
}

/** 结果数据 */
export interface ResultItem {
  /** 提问 id */
  id: string;
  /** 提问内容 */
  title: string;
  /** 来源 */
  source?: SourceType[];
  /** 使用的智能体 */
  agent?: Intelligence;
  /** 生成内容 */
  content: string;
  /** deepseek 思考 */
  thingking?: string;
  /** 提问时间 */
  time?: string;
  /** 子任务 */
  tasks?: TaskListTypeExt[];
  /** 任务状态: none-空状态, generating-子任务生成中,  generated-子任务生成完成, executing-执行子任务, summarizing-总结, finished-总结完成  */
  taskStatus?: 'none' | 'generating' | 'generated' | 'executing' | 'summarizing' | 'finished';
}

/** 会话数据 */
export interface ChatData {
  /** 会话列表 */
  chatList: ChatHistoryListItemResType[];
  /** 会话分页 */
  chatPageSize: number;
  /** 会话总数 */
  chatTotal: number;
}

export interface State {
  /** 当前提问相关信息 */
  currentAskInfo: CurrentAskInfo;
  /** 会话数据 */
  resultData: ResultItem[];
  /** 会话数据 */
  chatData: ChatData;
}

// 初始化数据
const initialState: State = {
  currentAskInfo: {
    id: '',
    parentId: undefined,
    sceneId: undefined,
    conversationId: undefined,
    fetchGenerating: false,
    fetchController: null,
    refIds: [],
  },
  resultData: [],
  chatData: {
    chatList: [],
    chatPageSize: 1,
    chatTotal: 0,
  },
};

export default initialState;
