import dayjs from 'dayjs';
import { v4 as uuid } from 'uuid';
import { StateCreator } from 'zustand';
import { getChatHistoryList } from '@/apis';
import initialState, { State, CurrentAskInfo, ResultItem, ChatData } from './states';
import fetchAgentChat from './fetch/fetchAgentChat';
import fetchAnswer from './fetch/fetchAnswer';
import fetchDocument from './fetch/fetchDocument';
import useAgent from '../useAgent';

// 使用映射类型构建参数类型
type UpdateParams = {
  [K in keyof CurrentAskInfo]?: CurrentAskInfo[K]; // K 为 CurrentAskInfo 的键，值为对应的类型
};

export interface Actions {
  /** 提问 */
  ask: (val: string) => void;
  /** 获取会话历史 */
  getChatHistory: () => Promise<void>;
  /** 重置会话结果 */
  resetResult: () => void;
  /** 重置控制器(暂停所有在执行中的请求) */
  resetController: () => void;
  /** 重置当前提问信息 */
  resetCurrentAskInfo: () => void;
  /** 更新当前提问信息 */
  // eslint-disable-next-line no-unused-vars
  updateCurrentAskInfo: (params: UpdateParams) => void;
  /** 更新结果数据 */
  // eslint-disable-next-line no-unused-vars
  updateResultData: (params: ResultItem[]) => void;
  /** 更新会话列表数据 */
  updateChatData: (params: ChatData) => void;
}

const createCounterActions: StateCreator<State & Actions, [], [], Actions> = (set, get) => ({
  ask: (val) => {
    const askId = uuid();
    const { chatData, getChatHistory } = get();
    const { typeId, typeCode, sessionId, ragflowId } = useAgent.getState();

    // 添加提问占位
    const addData = {
      id: askId,
      content: '',
      thingking: '',
      title: val,
      taskStatus: 'generating',
      time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    };

    set((state) => {
      return {
        // @ts-expect-error todo
        resultData: state.resultData.concat(addData),
        currentAskInfo: {
          ...state.currentAskInfo,
          id: askId,
          fetchGenerating: true,
          fetchController: new AbortController(),
        },
      };
    });

    // 自定义智能体
    if (typeCode === 'customAgent') {
      fetchAgentChat({
        id: askId,
        sessionId,
        ragflowId,
        userAgentId: `${typeId}`,
        content: val,
        onceFetchCb: () => {
          // 重置分页
          set({ chatData: { chatList: chatData.chatList, chatPageSize: 1, chatTotal: 0 } });
          // 更新提问列表
          getChatHistory();
        },
      });
    }

    // 轻墨文档
    if (typeCode === 'document') {
      fetchDocument({
        id: askId,
        content: val,
        onceFetchCb: () => {
          // 重置分页
          set({ chatData: { chatList: chatData.chatList, chatPageSize: 1, chatTotal: 0 } });
          // 更新提问列表
          getChatHistory();
        },
      });
    }

    // 文章，笔记，头脑风暴，邮件
    if (['article', 'note', 'head', 'mail'].includes(typeCode)) {
      fetchAnswer({
        id: askId,
        agent: typeCode,
        content: val,
        onceFetchCb: () => {
          // 重置分页
          set({ chatData: { chatList: chatData.chatList, chatPageSize: 1, chatTotal: 0 } });
          // 更新提问列表
          getChatHistory();
        },
      });
    }
  },
  getChatHistory: async () => {
    const { chatData } = get();
    const { typeId, typeCode } = useAgent.getState();

    try {
      const res = await getChatHistoryList({
        page_size: 50,
        page_num: chatData.chatPageSize,
        virtual_user_id: typeCode === 'customAgent' ? `${typeId}` : undefined,
        agentRequestType: typeCode === 'customAgent' ? undefined : typeCode,
      });

      const { chatPageSize, chatList } = chatData;
      set({
        chatData: {
          ...chatData,
          chatTotal: res.total,
          chatList: chatPageSize === 1 ? res.items : [...chatList, ...res.items],
        },
      });
    } catch (error) {
      console.log('[log getChatHistory]: ', error);
      throw error;
    }
  },
  resetResult: () => {
    set({ resultData: [] });
  },
  resetCurrentAskInfo: () => {
    set({ currentAskInfo: { ...initialState.currentAskInfo } });
  },
  resetController: () => {
    set((state) => {
      const { fetchController } = state.currentAskInfo;
      fetchController?.abort();

      return {
        currentAskInfo: {
          ...state.currentAskInfo,
          fetchGenerating: false,
          fetchController: null,
        },
        // 子任务暂停
        resultData: state.resultData.map((item) => {
          return {
            ...item,
            tasks: item.tasks?.map((iten) => {
              iten.controller?.abort();
              return { ...iten, status: iten.status === 20 ? 20 : 30, controller: null };
            }),
            taskStatus: 'finished',
          };
        }),
      };
    });
  },
  updateCurrentAskInfo: (params) => {
    set((state) => ({ currentAskInfo: { ...state.currentAskInfo, ...params } }));
  },
  updateResultData: (params) => {
    set(() => ({ resultData: params }));
  },
  updateChatData: (params) => {
    set(() => ({ chatData: params }));
  },
});

export default createCounterActions;
