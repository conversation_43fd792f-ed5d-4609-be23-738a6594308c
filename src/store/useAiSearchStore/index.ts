import { create } from 'zustand';
import initialState, { State } from './states';
import createCounterActions, { Actions } from './actions';

/** agent 聊天相关数据 store */
const useAiSearchStore = create<State & Actions>()((...args) => ({
  ...initialState,
  ...createCounterActions(...args),
}));

export default useAiSearchStore;

export * from './states';
export * from './actions';
export * from './derived';
