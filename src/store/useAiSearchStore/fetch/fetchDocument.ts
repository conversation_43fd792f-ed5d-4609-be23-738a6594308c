import { message } from '@echronos/antd';
import { getToken } from '@echronos/core';
// eslint-disable-next-line camelcase
import { unstable_batchedUpdates } from 'react-dom';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import useAiSearchStore from '@/store/useAiSearchStore';
import { State } from '../states';
import { Actions } from '../actions';

export interface FetchDocumentParams {
  id: string;
  content: string;
  onceFetchCb?: () => void;
  get?: () => State & Actions;
}

/**
 * 获取文档结果
 * @param id 提问 id
 * @param content 提问内容
 * @param onceFetchCb 一次性请求回调(用于刷新会话列表, 跳转页面逻辑)
 */
const fetchDocument = ({ id, content, onceFetchCb }: FetchDocumentParams) => {
  const { currentAskInfo, updateCurrentAskInfo, updateResultData } = useAiSearchStore.getState();
  const { sceneId, parentId, fetchController } = currentAskInfo;

  /** 文本数据字符串 */
  let sseText = '';

  /** 消息时间 */
  let onmessageTime = 0;

  let _onceFetchCb = onceFetchCb;

  /** 处理整个请求完成 */
  const handleAllDone = () => {
    try {
      unstable_batchedUpdates(() => {
        const { resultData } = useAiSearchStore.getState();

        // 更新状态
        updateResultData(
          resultData.map((item) => {
            if (item.id === id) {
              return { ...item, taskStatus: 'finished' };
            }
            return item;
          })
        );

        updateCurrentAskInfo({ fetchGenerating: false });
      });
    } catch (error) {
      window.console.log('[log error]: ', error);
    }
  };

  /** 处理单个请求完成 */
  const handleReqDone = (dataStr: string) => {
    try {
      const { resultData } = useAiSearchStore.getState();
      const responseData = JSON.parse(dataStr);
      window.console.log(responseData, 'responseData');
      // 文档
      if (responseData.data?.choices?.[0]?.aiMsgType === 'content') {
        sseText += responseData.data?.choices?.[0]?.data || '';
      }

      if (responseData.data?.status === 'error') {
        sseText += message.error('系统繁忙，请尝试简化请求或者检查网络连接后重试~');
      }

      unstable_batchedUpdates(() => {
        // 更新数据
        updateResultData(
          resultData.map((item) => {
            if (item.id === id) {
              return { ...item, content: sseText, taskStatus: 'summarizing' };
            }
            return item;
          })
        );

        // 相关 id 存在, 才继续处理
        const { recordId, sceneId: _sceneId, conversationId } = responseData.data;

        if (recordId && _sceneId) {
          // 更新 id 信息
          updateCurrentAskInfo({
            sceneId: _sceneId,
            parentId: recordId,
            conversationId: conversationId || undefined,
          });

          _onceFetchCb?.();
          _onceFetchCb = undefined;
        }
      });
    } catch (error) {
      window.console.log('[log error]: ', error);
    }
  };

  fetchEventSource(
    `${
      import.meta.env.BIZ_API_URL || 'https://gate-test.myhuahua.com/'
    }ech-ai-py/v1/openai/assistant`,
    {
      method: 'POST',
      headers: {
        authorization: getToken(),
        'Content-Type': 'application/json',
        Accept: 'text/event-stream;json',
      },
      body: JSON.stringify({
        aiType: 6,
        actionType: 34,
        content,
        parentId,
        sceneId,
        key_vals: {
          agentRequestType: 'document',
          'space-id': '',
          virtual_user_id: '419775030',
        },
        conversationId: currentAskInfo.conversationId,
      }),
      openWhenHidden: true,
      signal: fetchController?.signal,
      onmessage({ data }) {
        if (!data) return;
        if (!onmessageTime) {
          onmessageTime = new Date().getTime();
        }

        if (data === '[DONE]') {
          handleAllDone();
        } else {
          handleReqDone(data);
        }
      },
      onclose() {
        fetchController?.abort();
        unstable_batchedUpdates(() => updateCurrentAskInfo({ fetchGenerating: false }));
      },
      onerror(err) {
        message.error('系统繁忙，请尝试简化请求或者检查网络连接后重试~');
        fetchController?.abort();
        unstable_batchedUpdates(() => updateCurrentAskInfo({ fetchGenerating: false }));
        throw err;
      },
    }
  );
};

export default fetchDocument;
