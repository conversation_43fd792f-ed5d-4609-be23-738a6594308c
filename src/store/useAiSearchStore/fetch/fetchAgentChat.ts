import { message } from '@echronos/antd';
import { getToken } from '@echronos/core';
// eslint-disable-next-line camelcase
import { unstable_batchedUpdates } from 'react-dom';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import useAiSearchStore from '@/store/useAiSearchStore';
import { State } from '../states';
import { Actions } from '../actions';

export interface FetchAgentChatParams {
  id: string;
  sessionId: string;
  ragflowId: string;
  userAgentId: string;
  content: string;
  onceFetchCb?: () => void;
  get?: () => State & Actions;
}

/**
 * 智能体聊天
 * @param id 提问 id
 * @param sessionId 智能体会话id
 * @param ragflowId 智能体id
 * @param userAgentId: 虚拟用户id,
 * @param content 提问内容
 * @param onceFetchCb 一次性请求回调(用于刷新会话列表, 跳转页面逻辑)
 * @param get 获取 store 数据
 */
const fetchAgentChat = ({
  id,
  sessionId,
  ragflowId,
  userAgentId,
  content,
  onceFetchCb,
}: FetchAgentChatParams) => {
  const { currentAskInfo, updateCurrentAskInfo, updateResultData } = useAiSearchStore.getState();
  const { sceneId, parentId, conversationId: _conversationId, fetchController } = currentAskInfo;

  let _onceFetchCb = onceFetchCb;

  /** 文本数据字符串 */
  let sseText = '';

  /** 消息时间 */
  let onmessageTime = 0;

  /** 处理整个请求完成 */
  const handleAllDone = () => {
    try {
      unstable_batchedUpdates(() => {
        const { resultData } = useAiSearchStore.getState();

        // 更新状态
        updateResultData(
          resultData.map((item) => {
            if (item.id === id) {
              return { ...item, taskStatus: 'finished' };
            }
            return item;
          })
        );

        updateCurrentAskInfo({ fetchGenerating: false });
      });
    } catch (error) {
      window.console.log('[log error]: ', error);
    }
  };

  /** 处理单个请求完成 */
  const handleReqDone = (dataStr: string) => {
    try {
      const { resultData } = useAiSearchStore.getState();
      const responseData = JSON.parse(dataStr);
      // 智能体回复消息
      if (responseData.data?.choices?.[0]?.aiMsgType === 'content') {
        sseText += responseData.data?.choices?.[0]?.data || '';
      }

      unstable_batchedUpdates(() => {
        // 更新数据
        updateResultData(
          resultData.map((item) => {
            if (item.id === id) {
              return { ...item, content: sseText, taskStatus: 'summarizing' };
            }
            return item;
          })
        );

        // 相关 id 存在, 才继续处理
        const { recordId, sceneId: _sceneId, conversationId } = responseData.data;

        if (recordId && _sceneId) {
          // 更新 id 信息
          updateCurrentAskInfo({
            sceneId: _sceneId,
            parentId: recordId,
            conversationId: conversationId || sessionId || undefined,
          });

          _onceFetchCb?.();
          _onceFetchCb = undefined;
        }
      });
    } catch (error) {
      window.console.log('[log error]: ', error);
    }
  };

  fetchEventSource(
    `${
      import.meta.env.BIZ_API_URL || 'https://gate-test.myhuahua.com/'
    }ech-ai-py/v1/openai/assistant`,
    {
      method: 'POST',
      headers: {
        authorization: getToken(),
        'Content-Type': 'application/json',
        Accept: 'text/event-stream;json',
      },
      body: JSON.stringify({
        aiType: 7,
        actionType: 2000,
        content,
        key_vals: {
          agentRequestType: 'agentChat',
          'ragflow-id-params': ragflowId || 'b2d52b13203b475c86f3ecba021bad0a',
          virtual_user_id: userAgentId,
        },
        parentId,
        sceneId,
        conversationId: _conversationId || sessionId,
        scene: 'autogen_agent_chat',
      }),
      openWhenHidden: true,
      signal: fetchController?.signal,
      onmessage({ data }) {
        if (!data) return;
        if (!onmessageTime) {
          onmessageTime = new Date().getTime();
        }

        if (data === '[DONE]') {
          handleAllDone();
        } else {
          handleReqDone(data);
        }
      },
      onclose() {
        fetchController?.abort();
        unstable_batchedUpdates(() => updateCurrentAskInfo({ fetchGenerating: false }));
      },
      onerror(err) {
        message.error('系统繁忙，请尝试简化请求或者检查网络连接后重试~');
        fetchController?.abort();
        unstable_batchedUpdates(() => updateCurrentAskInfo({ fetchGenerating: false }));
        throw err;
      },
    }
  );
};

export default fetchAgentChat;
