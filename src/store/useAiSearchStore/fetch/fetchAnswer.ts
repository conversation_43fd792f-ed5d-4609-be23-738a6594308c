import { message } from '@echronos/antd';
import { getToken } from '@echronos/core';
// eslint-disable-next-line camelcase
import { unstable_batchedUpdates } from 'react-dom';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import useAiSearchStore from '@/store/useAiSearchStore';
import { State } from '../states';
import { Actions } from '../actions';

export interface FetchAnswerParams {
  id: string;
  agent: string;
  content: string;
  onceFetchCb?: () => void;
  get?: () => State & Actions;
}

/**
 * 获取提问结果(普通提问回答, 没有 @ 任何智能体)
 * @param id 提问 id
 * @param agent 智能体
 * @param content 提问内容
 * @param onceFetchCb 一次性请求回调(用于刷新会话列表, 跳转页面逻辑)
 */
const fetchAnswer = ({ id, agent, content, onceFetchCb }: FetchAnswerParams) => {
  const { currentAskInfo, updateCurrentAskInfo, updateResultData } = useAiSearchStore.getState();
  const { sceneId, parentId, fetchController } = currentAskInfo;

  let _onceFetchCb = onceFetchCb;

  // 'article'=17, 'note'=18,'mail'=19 'head'=21,
  /**
   * 获取提问结果(普通提问回答, 没有 @ 任何智能体)
   * @param article 写文章
   * @param note 写笔记
   * @param mail 写邮件
   * @param head 头脑风暴
   */

  const agentType = () => {
    switch (agent) {
      case 'article':
        return 17;
      case 'note':
        return 18;
      case 'mail':
        return 19;
      case 'head':
        return 21;
      default:
        return 17;
    }
  };

  const getVirtualUserId = () => {
    switch (agent) {
      case 'article':
        return '415904577';
      case 'note':
        return '415904987';
      case 'mail':
        return '415905134';
      case 'head':
        return '415904657';
      default:
        return '';
    }
  };

  /** 文本数据字符串 */
  let sseText = '';

  /** 消息时间 */
  let onmessageTime = 0;

  /** 处理整个请求完成 */
  const handleAllDone = () => {
    try {
      unstable_batchedUpdates(() => {
        const { resultData } = useAiSearchStore.getState();

        // 更新状态
        updateResultData(
          resultData.map((item) => {
            if (item.id === id) {
              return { ...item, taskStatus: 'finished' };
            }
            return item;
          })
        );

        updateCurrentAskInfo({ fetchGenerating: false });
      });
    } catch (error) {
      console.log('[log error]: ', error);
    }
  };

  /** 处理单个请求完成 */
  const handleReqDone = (dataStr: string) => {
    try {
      const { resultData } = useAiSearchStore.getState();
      const responseData = JSON.parse(dataStr);
      // 写作大师
      if (responseData.data?.choices?.[0]?.aiMsgType === 'content') {
        sseText += responseData.data?.choices?.[0]?.data || '';
      }

      unstable_batchedUpdates(() => {
        // 更新数据
        updateResultData(
          resultData.map((item) => {
            if (item.id === id) {
              return { ...item, content: sseText, taskStatus: 'summarizing' };
            }
            return item;
          })
        );

        // 相关 id 存在, 才继续处理
        const { recordId, sceneId: _sceneId, conversationId } = responseData.data;

        if (recordId && _sceneId) {
          // 更新 id 信息
          updateCurrentAskInfo({
            sceneId: _sceneId,
            parentId: recordId,
            conversationId: conversationId || undefined,
          });

          _onceFetchCb?.();
          _onceFetchCb = undefined;
        }
      });
    } catch (error) {
      console.log('[log error]: ', error);
    }
  };

  fetchEventSource(
    `${
      import.meta.env.BIZ_API_URL || 'https://gate-test.myhuahua.com/'
    }ech-ai-py/v1/openai/assistant`,
    {
      method: 'POST',
      headers: {
        authorization: getToken(),
        'Content-Type': 'application/json',
        Accept: 'text/event-stream;json',
      },
      body: JSON.stringify({
        aiType: 3,
        actionType: agentType(),
        content,
        parentId,
        sceneId,
        scene: 'write_master',
        key_vals: {
          agentRequestType: agent,
          virtual_user_id: getVirtualUserId(),
        },
      }),
      openWhenHidden: true,
      signal: fetchController?.signal,
      onmessage({ data }) {
        if (!data) return;
        if (!onmessageTime) {
          onmessageTime = new Date().getTime();
        }

        if (data === '[DONE]') {
          handleAllDone();
        } else {
          handleReqDone(data);
        }
      },
      onclose() {
        fetchController?.abort();
        unstable_batchedUpdates(() => updateCurrentAskInfo({ fetchGenerating: false }));
      },
      onerror(err) {
        message.error('系统繁忙，请尝试简化请求或者检查网络连接后重试~');
        fetchController?.abort();
        unstable_batchedUpdates(() => updateCurrentAskInfo({ fetchGenerating: false }));
        throw err;
      },
    }
  );
};

export default fetchAnswer;
