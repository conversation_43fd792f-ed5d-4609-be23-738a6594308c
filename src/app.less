html,
body {
  width: 100vw;
  height: 100vh;
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box;
  background-color: transparent !important;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

ol,
ul,
dl {
  margin: 0;
  padding: 0;
}

section,
header,
nav,
main,
aside,
footer {
  display: block;
}

img {
  max-width: 100%;
  max-height: 100%;

  &.error-white,
  &.error-gray {
    overflow: hidden;
    position: relative;

    &::after {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: #fff;
    }
  }

  &.error-gray {
    &::after {
      background: #f5f5f5;
    }
  }
}

// 应用 position fixed
.pages-wrapper {
  transform: none !important;
}

// 360急速游览器BUG
:focus {
  outline: 0;
}

/* 滚动条整体宽度 */
::-webkit-scrollbar {
  width: 6px; /* 你可以根据需要调整宽度 */
  height: 6px; /* 对于垂直滚动条，可以调整高度 */
  background-color: transparent;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: transparent; /* 轨道颜色 */
  border-radius: 6px; /* 圆角，可选 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #d9dbdf; /* 滑块颜色 */
  border-radius: 6px; /* 圆角，与轨道保持一致 */
}

/* 滑块在悬停时的样式 */
::-webkit-scrollbar-thumb:hover {
  background: #d9dbdf; /* 滑块颜色 */
}
