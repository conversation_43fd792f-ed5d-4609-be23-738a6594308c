/// <reference types="@echronos/ech-micro/client.d.ts" />
/// <reference types="@echronos/eslint-config/client.d.ts" />

interface ImportMetaEnv {
  readonly BIZ_API_URL: string;

  readonly BIZ_ORIGIN_PUBLIC_URL: string;

  readonly BIZ_APP_PLATFORM_NO: string;

  readonly BIZ_UM_SITE: string;

  readonly BIZ_ECHOS_DOMAIN: string;
  readonly BIZ_AGENTS_CHAT_APP_DOMAIN: string;

  readonly BIZ_UM_VITE_SITE_ID: string;

  readonly BIZ_OBS_FOLDER: string;
  readonly BIZ_API_TIMEOUT: string;
  readonly BIZ_API_SA_TYPE: string;
}

declare module '*.less' {
  const content: { [key: string]: string };
  export default content;
}
