// @ts-check
import { fileURLToPath, URL } from 'node:url';
import { dirname, resolve as pathResolve } from 'node:path';
import { readFileSync, mkdirSync, statSync, writeFileSync } from 'node:fs';

// eslint-disable-next-line no-underscore-dangle
export const __dirname = fileURLToPath(new URL('.', import.meta.url));

export const resolve = (...paths) => pathResolve(__dirname, ...paths);

export const fileExists = (filePath) => {
  try {
    const stat = statSync(filePath);
    return !!stat && stat.isFile();
  } catch (e) {
    return false;
  }
};

export const dirExists = (dirPath) => {
  try {
    const stat = statSync(dirPath);
    return !!stat && stat.isDirectory();
  } catch (e) {
    return false;
  }
};

export const readFile = (filePath) => {
  if (fileExists(filePath)) {
    return readFileSync(filePath, { encoding: 'utf-8' });
  }
  return '';
};

export const readJSONFile = (jsonPath) => {
  const json = readFile(jsonPath);
  if (json) {
    try {
      return JSON.parse(json);
    } catch (e) {
      return {};
    }
  }
  return {};
};

export const writeFile = (filePath, content, isAppend) => {
  if (content === null || typeof content === 'undefined') {
    return;
  }

  const dirPath = dirname(filePath);
  if (!dirExists(dirPath)) {
    mkdirSync(dirPath, { recursive: true });
  }

  let fileContent = '';
  if (typeof content === 'object') {
    fileContent = JSON.stringify(content, null, 2);
  } else if (typeof content === 'string' || typeof content === 'number') {
    fileContent = content;
  }

  writeFileSync(filePath, fileContent, { encoding: 'utf-8', flag: isAppend ? 'a' : 'w' });
};
