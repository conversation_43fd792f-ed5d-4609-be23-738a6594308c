// eslint-disable-next-line import/extensions
import { readJSONFile, resolve, writeFile } from './util.js';

const config = readJSONFile(resolve('../package.json'));
const timestamp = Date.now();
writeFile(
  resolve('../dist/version.json'),
  JSON.stringify({
    hash: JSON.stringify(timestamp),
    timestamp,
    updated: new Date(),
    version: config.version,
    updatedName: config.name,
  })
);
