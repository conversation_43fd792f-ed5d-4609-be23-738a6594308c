{"root": true, "extends": ["@echronos/eslint-config"], "rules": {"import/no-unresolved": ["error", {"ignore": ["antd"]}], "jsx-a11y/control-has-associated-label": "off", "jsx-a11y/no-static-element-interactions": "off", "no-console": "warn", "arrow-body-style": "off", "array-callback-return": "off", "consistent-return": "off", "no-unused-vars": "warn", "import/no-cycle": "off", "eslint-disable react/require-default-props": "off", "no-underscore-dangle": "off", "camelcase": "off"}, "env": {"node": true, "browser": true}, "ignorePatterns": ["dist", "dist-ssr", "*.min.js", "micro.*.js", "micro.*.ts"]}