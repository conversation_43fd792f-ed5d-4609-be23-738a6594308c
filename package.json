{"name": "agents", "version": "1.0.0", "private": true, "type": "module", "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "npx simple-git-hooks", "dev": "cross-env NODE_ENV=development ech-micro --port 9014", "build": "cross-env NODE_ENV=production tsc && ech-micro build && pnpm run version", "tsc": "tsc", "version": "node ./scripts/generate-version.js", "lint": "pnpm run lint:script && pnpm run lint:style", "lint:fix": "pnpm run lint:script --fix && pnpm run lint:style --fix", "lint:script": "eslint **/*.{js,jsx,ts,tsx}", "lint:style": "stylelint \"src/**/*.{less,css}\""}, "dependencies": {"@ant-design/icons": "~4.7.0", "@ebay/nice-modal-react": "^1.2.13", "@echronos/antd": "^4.24.17", "@echronos/core": "latest", "@echronos/echos-icon": "^1.0.12", "@echronos/echos-ui": "2.0.0", "@echronos/editor": "1.3.32", "@echronos/icons": "^0.0.2", "@echronos/millet-ui": "1.4.40", "@echronos/react": "^0.0.4", "@loadable/component": "^5.15.3", "@microsoft/fetch-event-source": "^2.0.1", "@nutui/icons-react": "^3.0.1", "@rc-component/portal": "^1.1.2", "@tiptap/core": "^2.1.11", "@tiptap/extension-bullet-list": "^2.10.3", "@tiptap/extension-collaboration": "2.1.12", "@tiptap/extension-color": "^2.10.3", "@tiptap/extension-focus": "^2.10.3", "@tiptap/extension-highlight": "^2.10.3", "@tiptap/extension-list-item": "^2.10.3", "@tiptap/extension-ordered-list": "^2.10.3", "@tiptap/extension-placeholder": "2.4.0", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/extension-underline": "^2.10.3", "@tiptap/pm": "^2.1.11", "@tiptap/react": "^2.1.11", "@tiptap/starter-kit": "^2.1.11", "@tiptap/suggestion": "^2.1.11", "@types/chance": "^1.1.6", "ahooks": "~3.7.8", "animate.css": "^4.1.1", "antd": "~4.20.7", "chance": "^1.1.12", "classnames": "~2.3.2", "copy-to-clipboard": "~3.3.3", "core-js": "^3.31.0", "dayjs": "~1.11.8", "html2canvas": "^1.4.1", "jsplumb": "^2.15.6", "lodash": "~4.17.21", "mitt": "^3.0.1", "mobx": "~6.9.0", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-color": "^2.17.3", "react-dom": "^17.0.2", "react-helmet": "~6.1.0", "react-infinite-scroll-component": "~6.1.0", "react-markdown": "^9.0.1", "react-masonry-css": "^1.0.16", "react-mentions": "^4.4.10", "react-router-dom": "~6.3.0", "react-syntax-highlighter": "^15.6.1", "react-window": "~1.8.9", "remark-gfm": "^4.0.0", "styled-components": "6.0.0-rc.3", "uuid": "^9.0.1", "y-indexeddb": "^9.0.12", "yjs": "^13.6.20", "zustand": "^4.5.5"}, "devDependencies": {"@echronos/ech-micro": "^0.0.5", "@echronos/eslint-config": "^0.0.1", "@echronos/swc-plugin-transform-imports": "^1.6.0", "@echronos/user": "^1.0.0", "@swc/core": "1.3.105", "@swc/jest": "^0.2.26", "@swc/types": "0.1.5", "@types/loadable__component": "^5.13.5", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/react": "^17.0.62", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-color": "^3.0.6", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-dom": "^17.0.20", "@types/react-helmet": "^6.1.6", "@types/react-mentions": "^4.4.1", "@types/react-slick": "^0.23.10", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.5", "@types/uuid": "^10.0.0", "cross-env": "^7.0.3", "eslint": "~8.22.0", "http-proxy-middleware": "^2.0.6", "jest": "^29.5.0", "less": "^4.1.3", "lint-staged": "^12.3.7", "prettier": "^2.8.8", "react-image-crop": "^10.0.7", "simple-git-hooks": "^2.8.1", "stylelint": "^15.8.0", "typescript": "^5.1.3", "webpack-chain": "^6.5.1"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged --concurrent false"}, "lint-staged": {"**/*.{ts,tsx,less,css,json,js,jsx}": ["prettier --write --cache --ignore-unknown"], "**/*.{js,jsx,ts,tsx}": ["eslint --cache --fix"]}}