import { defineConfig, loadEnv } from '@echronos/ech-micro';
// import proxyMiddleware from 'http-proxy-middleware';

const envConfig = loadEnv(process.env.NODE_ENV, './', 'BIZ_');
console.log('envConfig', envConfig);

export default defineConfig({
  name: 'wilTree',
  base: envConfig.BIZ_AGENTS_CHAT_APP_DOMAIN || '/',
  compiler: 'swc',
  webpackChain: (chain) => {
    if (process.env.NODE_ENV === 'production') {
      chain
        .externals({
          react: 'React',
          'react-dom': 'ReactDOM',
          'react-is': 'ReactIs',
          'react/jsx-runtime': 'ReactJsx',
          'react/jsx-dev-runtime': 'ReactJsxDev',
          'react-helmet': 'ReactHelmet',

          qs: 'Qs',
          axios: 'axios',
          dayjs: 'dayjs',
          classnames: 'classNames',
          localforage: 'localforage',
          'copy-to-clipboard': 'CopyToClipboard',
          'react-copy-to-clipboard': 'ReactCopyToClipboard',

          '@echronos/core': 'ECore',
          '@echronos/user': 'EUser',
        })
        .plugin('html')
        .tap((opts) => {
          const opt = opts[0] || {};
          return [
            {
              ...opt,
              templateContent: (ctx) => {
                const { templateContent } = opt;
                const content =
                  typeof templateContent === 'function' ? templateContent(ctx) : templateContent;
                return content.replace(
                  '</body>',
                  `<script src="${envConfig.BIZ_ORIGIN_PUBLIC_URL}/static/vendor/runtime/v1.3/runtime.js" exclude></script>
<script src="${envConfig.BIZ_ORIGIN_PUBLIC_URL}/static/vendor/runtime/v1.3/echronos.js" exclude></script>
</body>`
                );
              },
            },
          ];
        })
        .end();
    }

    return chain;
  },
});
